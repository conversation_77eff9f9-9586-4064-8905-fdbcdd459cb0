import '../models/category_model.dart';
import '../models/hotel_model.dart';

class HomeDataService {
  // Dummy categories data
  static List<CategoryModel> getCategories() {
    return [
      const CategoryModel(
        id: '1',
        name: 'Mountain',
        icon: '🏔️',
        isSelected: true,
      ),
      const CategoryModel(
        id: '2',
        name: 'Beach',
        icon: '🏖️',
      ),
      const CategoryModel(
        id: '3',
        name: 'Design',
        icon: '🏛️',
      ),
      const CategoryModel(
        id: '4',
        name: 'Island',
        icon: '🏝️',
      ),
    ];
  }

  // Dummy hotels data with real image URLs
  static List<HotelModel> getMostPopularHotels() {
    return [
      const HotelModel(
        id: '1',
        name: 'Mountain View Resort',
        location: 'Kilimanjaro, Tanzania',
        price: 120.0,
        rating: 4.8,
        beds: 2,
        baths: 2,
        guests: 4,
        imageUrl: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400&h=300&fit=crop',
        category: 'Mountain',
        isFavorite: true,
        reviewCount: 127,
      ),
      const HotelModel(
        id: '2',
        name: 'Serengeti Safari Lodge',
        location: 'Serengeti, Tanzania',
        price: 250.0,
        rating: 4.9,
        beds: 1,
        baths: 1,
        guests: 2,
        imageUrl: 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=400&h=300&fit=crop',
        category: 'Mountain',
        reviewCount: 89,
      ),
      const HotelModel(
        id: '3',
        name: 'Zanzibar Beach Resort',
        location: 'Zanzibar, Tanzania',
        price: 180.0,
        rating: 4.7,
        beds: 3,
        baths: 2,
        guests: 6,
        imageUrl: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop',
        category: 'Beach',
        isFavorite: true,
        reviewCount: 203,
      ),
      const HotelModel(
        id: '4',
        name: 'Dar es Salaam Hotel',
        location: 'Dar es Salaam, Tanzania',
        price: 95.0,
        rating: 4.5,
        beds: 2,
        baths: 1,
        guests: 3,
        imageUrl: 'https://images.unsplash.com/photo-1564501049412-61c2a3083791?w=400&h=300&fit=crop',
        category: 'Design',
        reviewCount: 156,
      ),
      const HotelModel(
        id: '5',
        name: 'Pemba Island Lodge',
        location: 'Pemba Island, Tanzania',
        price: 200.0,
        rating: 4.6,
        beds: 2,
        baths: 2,
        guests: 4,
        imageUrl: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=300&fit=crop',
        category: 'Island',
        reviewCount: 78,
      ),
      const HotelModel(
        id: '6',
        name: 'Arusha Coffee Lodge',
        location: 'Arusha, Tanzania',
        price: 140.0,
        rating: 4.4,
        beds: 1,
        baths: 1,
        guests: 2,
        imageUrl: 'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400&h=300&fit=crop',
        category: 'Mountain',
        reviewCount: 92,
      ),
    ];
  }

  // Get hotels by category
  static List<HotelModel> getHotelsByCategory(String category) {
    final allHotels = getMostPopularHotels();
    if (category == 'All' || category.isEmpty) {
      return allHotels;
    }
    return allHotels.where((hotel) => hotel.category == category).toList();
  }

  // Search hotels
  static List<HotelModel> searchHotels(String query) {
    final allHotels = getMostPopularHotels();
    if (query.isEmpty) {
      return allHotels;
    }
    
    return allHotels.where((hotel) {
      return hotel.name.toLowerCase().contains(query.toLowerCase()) ||
             hotel.location.toLowerCase().contains(query.toLowerCase()) ||
             hotel.category.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
