import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../authentication/data/models/user_model.dart';
import '../../data/models/category_model.dart';
import '../../data/models/hotel_model.dart';
import '../../data/services/home_data_service.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/category_chip_widget.dart';
import '../widgets/hotel_card_widget.dart';
import 'house_detail_screen.dart';

class ExploreScreen extends StatefulWidget {
  final bool isAuthenticated;
  final UserModel? currentUser;

  const ExploreScreen({
    super.key,
    this.isAuthenticated = false,
    this.currentUser,
  });

  @override
  State<ExploreScreen> createState() => _ExploreScreenState();
}

class _ExploreScreenState extends State<ExploreScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<CategoryModel> _categories = [];
  List<HotelModel> _hotels = [];
  List<HotelModel> _filteredHotels = [];
  String _selectedCategory = 'Mountain';
  bool _isScrolled = false;
  bool _showNotificationPopup = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isScrolled = _scrollController.offset > 50;
    if (isScrolled != _isScrolled) {
      setState(() {
        _isScrolled = isScrolled;
      });
    }
  }

  void _showNotificationDialog() {
    setState(() {
      _showNotificationPopup = true;
    });
  }

  void _showAuthenticatedNotifications() {
    // TODO: Show actual notifications for authenticated users
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('No new notifications'),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _hideNotificationDialog() {
    setState(() {
      _showNotificationPopup = false;
    });
  }

  void _navigateToLogin() {
    _hideNotificationDialog();
    context.go('/login');
  }

  void _loadData() {
    setState(() {
      _categories = HomeDataService.getCategories();
      _hotels = HomeDataService.getMostPopularHotels();
      _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
    });
  }

  void _onCategorySelected(CategoryModel category) {
    setState(() {
      // Update categories selection
      _categories = _categories.map((cat) {
        return cat.copyWith(isSelected: cat.id == category.id);
      }).toList();

      _selectedCategory = category.name;
      _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
      } else {
        _filteredHotels = HomeDataService.searchHotels(query);
      }
    });
  }

  void _onHotelTap(HotelModel hotel) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => HouseDetailScreen(hotel: hotel),
      ),
    );
  }

  void _onFavoriteToggle(HotelModel hotel) {
    setState(() {
      final index = _filteredHotels.indexWhere((h) => h.id == hotel.id);
      if (index != -1) {
        _filteredHotels[index] = hotel.copyWith(isFavorite: !hotel.isFavorite);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          hotel.isFavorite 
            ? 'Removed from favorites' 
            : 'Added to favorites'
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    const headerHeight = 240.0; // Fixed height for the header card

    return Scaffold(
      backgroundColor: AppColors.background,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        toolbarHeight: 0, // Hide the app bar content
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      body: Stack(
        children: [
          // Main scrollable content with top padding
          CustomScrollView(
            controller: _scrollController,
            slivers: [
              // Spacer to push content below the pinned header
              SliverToBoxAdapter(
                child: SizedBox(height: headerHeight + MediaQuery.of(context).padding.top),
              ),

              // Most Popular Section
              _buildMostPopularSection(),
            ],
          ),

          // White background behind header card
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: headerHeight + MediaQuery.of(context).padding.top,
            child: Container(
              color: AppColors.background,
            ),
          ),

          // Pinned header card that stays on top
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: _buildStaticHeaderCard(),
          ),

          // Notification popup overlay
          if (_showNotificationPopup) _buildNotificationPopup(),
        ],
      ),
    );
  }

  Widget _buildStaticHeaderCard() {
    return Container(
      margin: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title with user greeting and notification icon
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.isAuthenticated && widget.currentUser != null) ...[
                        Text(
                          'Hello, ${widget.currentUser!.firstName}!',
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Find your perfect stay',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ] else ...[
                        Text(
                          'Find your perfect stay',
                          style: AppTextStyles.headlineSmall.copyWith(
                            color: AppColors.textPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: widget.isAuthenticated ? _showAuthenticatedNotifications : _showNotificationDialog,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      widget.isAuthenticated ? Icons.notifications : Icons.notifications_outlined,
                      color: AppColors.primary,
                      size: 22,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Search Bar
            SearchBarWidget(
              controller: _searchController,
              onChanged: _onSearchChanged,
            ),

            const SizedBox(height: 16),

            // Categories with horizontal tabs
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Categories',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 40, // Increased height for better visibility
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.zero,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.only(
                          right: index < _categories.length - 1 ? 12 : 0,
                        ),
                        child: CategoryChipWidget(
                          category: _categories[index],
                          onTap: _onCategorySelected,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationPopup() {
    return Container(
      color: Colors.black.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header with X button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Notifications',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  GestureDetector(
                    onTap: _hideNotificationDialog,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.textSecondary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Notification icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.notifications_outlined,
                  size: 40,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 24),

              // Message
              Text(
                'Please log in to view your notifications',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              Text(
                'You need to be logged in to receive and view notifications about your bookings, updates, and special offers.',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Login button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _navigateToLogin,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Log In',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMostPopularSection() {
    return SliverToBoxAdapter(
      child: Container(
        color: AppColors.background, // Ensure background color
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Add spacing from the header card
            const SizedBox(height: 24),

            // Most Popular header with background
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Most Popular',
                    style: AppTextStyles.headlineMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // TODO: Navigate to see all
                    },
                    child: Text(
                      'See All',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content container
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  const SizedBox(height: 16),

                  // Hotels List
                  if (_filteredHotels.isEmpty)
                    _buildEmptyState()
                  else
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _filteredHotels.length,
                      itemBuilder: (context, index) {
                        return HotelCardWidget(
                          hotel: _filteredHotels[index],
                          onTap: _onHotelTap,
                          onFavoriteToggle: _onFavoriteToggle,
                        );
                      },
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No hotels found',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or category',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
