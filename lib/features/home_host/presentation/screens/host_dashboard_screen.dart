import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../authentication/presentation/providers/auth_provider.dart';
import '../providers/host_provider.dart';
import '../widgets/host_bottom_navigation.dart';
import 'host_today_screen.dart';
import 'host_bookings_screen.dart';
import 'host_listings_screen.dart';
import 'host_profile_screen.dart';

class HostDashboardScreen extends StatefulWidget {
  const HostDashboardScreen({super.key});

  @override
  State<HostDashboardScreen> createState() => _HostDashboardScreenState();
}

class _HostDashboardScreenState extends State<HostDashboardScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HostTodayScreen(),
    const HostBookingsScreen(),
    const HostListingsScreen(),
    const HostProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HostProvider>().loadDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) return;
        _handleBackPress();
      },
      child: Scaffold(
        backgroundColor: AppColors.background,
        body: IndexedStack(
          index: _currentIndex,
          children: _screens,
        ),
        bottomNavigationBar: HostBottomNavigation(
          currentIndex: _currentIndex,
          onTap: _handleTabTap,
        ),
      ),
    );
  }

  void _handleTabTap(int index) {
    // Check if profile is complete for restricted tabs
    if ((index == 1 || index == 2) && !_isProfileComplete()) {
      _showCompleteProfileDialog();
      return;
    }

    setState(() {
      _currentIndex = index;
    });

    // Load data based on selected tab
    final hostProvider = context.read<HostProvider>();
    switch (index) {
      case 0: // Today
        hostProvider.loadDashboardData();
        break;
      case 1: // Bookings
        hostProvider.loadBookingsData();
        break;
      case 2: // Listings
        hostProvider.loadPropertiesData();
        break;
      case 3: // Profile
        // Profile data is handled by AuthProvider
        break;
    }
  }

  bool _isProfileComplete() {
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.currentUser;

    // Check if user has completed profile (has date of birth, address, and NIDA)
    // This is a simple check - you might want to add more sophisticated logic
    return user != null &&
           user.firstName != null &&
           user.lastName != null &&
           user.email != null;
  }

  void _showCompleteProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Complete Your Profile',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Please complete your profile to access bookings and listings. This helps us verify your identity and provide better service.',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Later',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/host-complete-profile');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.textOnPrimary,
            ),
            child: Text(
              'Complete Profile',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textOnPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleBackPress() {
    // Show exit confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Exit App',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to exit the app?',
          style: AppTextStyles.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Exit the app
              // SystemNavigator.pop(); // Uncomment if needed
            },
            child: Text(
              'Exit',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
