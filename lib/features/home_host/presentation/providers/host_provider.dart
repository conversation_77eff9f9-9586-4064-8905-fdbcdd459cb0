import 'package:flutter/foundation.dart';
import '../../../../core/utils/logger.dart';
import '../../../authentication/data/models/register_request_model.dart';
import '../../data/repositories/host_repository.dart';

enum HostState {
  initial,
  loading,
  loaded,
  error,
}

class HostProvider extends ChangeNotifier {
  final HostRepository _hostRepository;

  HostProvider({HostRepository? hostRepository})
      : _hostRepository = hostRepository ?? HostRepositoryImpl();

  // State management
  HostState _state = HostState.initial;
  String? _errorMessage;
  String? _successMessage;

  // Dashboard data
  Map<String, dynamic>? _dashboardData;
  Map<String, dynamic>? _bookingsData;
  Map<String, dynamic>? _propertiesData;

  // Profile completion status
  bool _isProfileComplete = false;

  // Getters
  HostState get state => _state;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;
  Map<String, dynamic>? get dashboardData => _dashboardData;
  Map<String, dynamic>? get bookingsData => _bookingsData;
  Map<String, dynamic>? get propertiesData => _propertiesData;
  bool get isProfileComplete => _isProfileComplete;
  bool get isLoading => _state == HostState.loading;

  // Complete profile
  Future<bool> completeProfile(CompleteProfileRequestModel request) async {
    _setState(HostState.loading);
    _clearMessages();

    try {
      final response = await _hostRepository.completeProfile(request);

      if (response.success) {
        _isProfileComplete = true;
        _successMessage = response.message ?? 'Profile completed successfully!';
        _setState(HostState.loaded);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to complete profile';
        _setState(HostState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(HostState.error);
      Logger.error('HostProvider: Complete profile failed - $e');
      return false;
    }
  }

  // Load dashboard data
  Future<void> loadDashboardData() async {
    _setState(HostState.loading);
    _clearMessages();

    try {
      _dashboardData = await _hostRepository.getDashboardData();
      _setState(HostState.loaded);
    } catch (e) {
      _errorMessage = 'Failed to load dashboard data';
      _setState(HostState.error);
      Logger.error('HostProvider: Load dashboard data failed - $e');
    }
  }

  // Load bookings data
  Future<void> loadBookingsData() async {
    _setState(HostState.loading);
    _clearMessages();

    try {
      _bookingsData = await _hostRepository.getBookings();
      _setState(HostState.loaded);
    } catch (e) {
      _errorMessage = 'Failed to load bookings data';
      _setState(HostState.error);
      Logger.error('HostProvider: Load bookings data failed - $e');
    }
  }

  // Load properties data
  Future<void> loadPropertiesData() async {
    _setState(HostState.loading);
    _clearMessages();

    try {
      _propertiesData = await _hostRepository.getProperties();
      _setState(HostState.loaded);
    } catch (e) {
      _errorMessage = 'Failed to load properties data';
      _setState(HostState.error);
      Logger.error('HostProvider: Load properties data failed - $e');
    }
  }

  // Refresh all data
  Future<void> refreshData() async {
    await Future.wait([
      loadDashboardData(),
      loadBookingsData(),
      loadPropertiesData(),
    ]);
  }

  // Helper methods
  void _setState(HostState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  // Clear all data
  void clearData() {
    _dashboardData = null;
    _bookingsData = null;
    _propertiesData = null;
    _isProfileComplete = false;
    _setState(HostState.initial);
    _clearMessages();
  }
}
