import '../../../../core/constants/api_constants.dart';
import '../../../../core/services/http_client.dart';
import '../../../../core/utils/logger.dart';
import '../../../authentication/data/models/auth_response.dart';
import '../../../authentication/data/models/register_request_model.dart';

class HostApiService {
  final HttpClient _httpClient;

  HostApiService({HttpClient? httpClient})
      : _httpClient = httpClient ?? HttpClient.instance;

  // Complete host profile
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request) async {
    try {
      Logger.info('HostApiService: Completing host profile');
      
      final response = await _httpClient.post(
        ApiConstants.userUpdate, // Using existing update endpoint
        data: request.toJson(),
      );
      
      Logger.info('HostApiService: Complete profile response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('HostApiService: Complete profile failed - $e');
      rethrow;
    }
  }

  // Get host dashboard data
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      Logger.info('HostApiService: Getting dashboard data');
      
      // TODO: Replace with actual dashboard endpoint when available
      final response = await _httpClient.get('/host/dashboard');
      
      Logger.info('HostApiService: Dashboard data received');
      return response;
    } catch (e) {
      Logger.error('HostApiService: Get dashboard data failed - $e');
      // Return mock data for now
      return {
        'properties_count': 0,
        'bookings_count': 0,
        'revenue': 0,
        'occupancy_rate': 0,
        'recent_bookings': [],
        'properties': [],
      };
    }
  }

  // Get host bookings
  Future<Map<String, dynamic>> getBookings() async {
    try {
      Logger.info('HostApiService: Getting host bookings');
      
      final response = await _httpClient.get(ApiConstants.bookingsOwner);
      
      Logger.info('HostApiService: Bookings data received');
      return response;
    } catch (e) {
      Logger.error('HostApiService: Get bookings failed - $e');
      // Return mock data for now
      return {
        'bookings': [],
        'total': 0,
      };
    }
  }

  // Get host properties
  Future<Map<String, dynamic>> getProperties() async {
    try {
      Logger.info('HostApiService: Getting host properties');
      
      final response = await _httpClient.get(ApiConstants.housesOwner);
      
      Logger.info('HostApiService: Properties data received');
      return response;
    } catch (e) {
      Logger.error('HostApiService: Get properties failed - $e');
      // Return mock data for now
      return {
        'properties': [],
        'total': 0,
      };
    }
  }
}
