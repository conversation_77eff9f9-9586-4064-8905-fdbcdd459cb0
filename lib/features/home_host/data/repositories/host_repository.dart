import '../../../../core/utils/logger.dart';
import '../../../authentication/data/models/auth_response.dart';
import '../../../authentication/data/models/register_request_model.dart';
import '../services/host_api_service.dart';

abstract class HostRepository {
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request);
  Future<Map<String, dynamic>> getDashboardData();
  Future<Map<String, dynamic>> getBookings();
  Future<Map<String, dynamic>> getProperties();
}

class HostRepositoryImpl implements HostRepository {
  final HostApiService _apiService;

  HostRepositoryImpl({HostApiService? apiService})
      : _apiService = apiService ?? HostApiService();

  @override
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request) async {
    try {
      Logger.info('HostRepository: Complete profile request');
      final response = await _apiService.completeProfile(request);
      Logger.info('HostRepository: Complete profile completed');
      return response;
    } catch (e) {
      Logger.error('HostRepository: Complete profile failed - $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getDashboardData() async {
    try {
      Logger.info('HostRepository: Get dashboard data request');
      final response = await _apiService.getDashboardData();
      Logger.info('HostRepository: Get dashboard data completed');
      return response;
    } catch (e) {
      Logger.error('HostRepository: Get dashboard data failed - $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getBookings() async {
    try {
      Logger.info('HostRepository: Get bookings request');
      final response = await _apiService.getBookings();
      Logger.info('HostRepository: Get bookings completed');
      return response;
    } catch (e) {
      Logger.error('HostRepository: Get bookings failed - $e');
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>> getProperties() async {
    try {
      Logger.info('HostRepository: Get properties request');
      final response = await _apiService.getProperties();
      Logger.info('HostRepository: Get properties completed');
      return response;
    } catch (e) {
      Logger.error('HostRepository: Get properties failed - $e');
      rethrow;
    }
  }
}
