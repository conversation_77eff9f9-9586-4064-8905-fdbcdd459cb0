import 'package:go_router/go_router.dart';
import '../presentation/screens/my_listings_screen.dart';
import '../presentation/screens/basic_property_info_screen.dart';
import '../presentation/screens/amenities_screen.dart';
import '../presentation/screens/pricing_screen.dart';
import '../presentation/screens/photos_upload_screen.dart';
import '../presentation/screens/availability_screen.dart';
import '../presentation/screens/preview_review_screen.dart';
import '../presentation/screens/publish_success_screen.dart';

class ListingRoutes {
  static const String myListings = '/host/listings';
  static const String createBasicInfo = '/host/listings/create/:listingId/basic';
  static const String createAmenities = '/host/listings/create/:listingId/amenities';
  static const String createPricing = '/host/listings/create/:listingId/pricing';
  static const String createPhotos = '/host/listings/create/:listingId/photos';
  static const String createAvailability = '/host/listings/create/:listingId/availability';
  static const String createPreview = '/host/listings/create/:listingId/preview';
  static const String publishSuccess = '/host/listings/:listingId/publish-success';

  static List<RouteBase> get routes => [
    // My Listings Screen
    GoRoute(
      path: myListings,
      name: 'host-listings',
      builder: (context, state) => const MyListingsScreen(),
    ),

    // Listing Creation Flow
    GoRoute(
      path: createBasicInfo,
      name: 'listing-basic-info',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return BasicPropertyInfoScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: createAmenities,
      name: 'listing-amenities',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return AmenitiesScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: createPricing,
      name: 'listing-pricing',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return PricingScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: createPhotos,
      name: 'listing-photos',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return PhotosUploadScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: createAvailability,
      name: 'listing-availability',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return AvailabilityScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: createPreview,
      name: 'listing-preview',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return PreviewReviewScreen(listingId: listingId);
      },
    ),
    GoRoute(
      path: publishSuccess,
      name: 'listing-publish-success',
      builder: (context, state) {
        final listingId = state.pathParameters['listingId']!;
        return PublishSuccessScreen(listingId: listingId);
      },
    ),
  ];
}
