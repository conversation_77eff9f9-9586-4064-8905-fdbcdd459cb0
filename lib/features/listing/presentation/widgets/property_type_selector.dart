import 'package:flutter/material.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../data/models/listing_model.dart';

class PropertyTypeSelector extends StatelessWidget {
  final PropertyType selectedType;
  final Function(PropertyType) onTypeSelected;

  const PropertyTypeSelector({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    final propertyTypes = [
      {
        'type': PropertyType.apartment,
        'title': 'Apartment',
        'description': 'A place that\'s part of a building',
        'icon': Icons.apartment,
      },
      {
        'type': PropertyType.house,
        'title': 'House',
        'description': 'A standalone residential building',
        'icon': Icons.house,
      },
      {
        'type': PropertyType.villa,
        'title': 'Villa',
        'description': 'A luxury house with garden',
        'icon': Icons.villa,
      },
      {
        'type': PropertyType.studio,
        'title': 'Studio',
        'description': 'A single room with kitchenette',
        'icon': Icons.home,
      },
      {
        'type': PropertyType.room,
        'title': 'Room',
        'description': 'A private room in a shared space',
        'icon': Icons.bed,
      },
      {
        'type': PropertyType.hotel,
        'title': 'Hotel Room',
        'description': 'A room in a hotel or lodge',
        'icon': Icons.hotel,
      },
      {
        'type': PropertyType.guesthouse,
        'title': 'Guesthouse',
        'description': 'A small accommodation facility',
        'icon': Icons.home_work,
      },
    ];

    return Column(
      children: propertyTypes.map((property) {
        final type = property['type'] as PropertyType;
        final isSelected = selectedType == type;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildPropertyTypeCard(
            type: type,
            title: property['title'] as String,
            description: property['description'] as String,
            icon: property['icon'] as IconData,
            isSelected: isSelected,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPropertyTypeCard({
    required PropertyType type,
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () => onTypeSelected(type),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: AppColors.shadow.withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppColors.primary 
                    : AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected 
                    ? AppColors.textOnPrimary 
                    : AppColors.primary,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            
            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected 
                          ? AppColors.primary 
                          : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            
            // Selection indicator
            if (isSelected)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: AppColors.textOnPrimary,
                  size: 16,
                ),
              )
            else
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.border, width: 2),
                  shape: BoxShape.circle,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
