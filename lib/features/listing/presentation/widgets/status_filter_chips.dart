import 'package:flutter/material.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../data/models/listing_model.dart';

class StatusFilterChips extends StatelessWidget {
  final ListingStatus? selectedStatus;
  final Function(ListingStatus?) onStatusChanged;
  final Map<ListingStatus, int> statusCounts;

  const StatusFilterChips({
    super.key,
    this.selectedStatus,
    required this.onStatusChanged,
    required this.statusCounts,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildFilterChip(
            label: 'All',
            count: statusCounts.values.fold(0, (sum, count) => sum + count),
            isSelected: selectedStatus == null,
            onTap: () => onStatusChanged(null),
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            label: 'Draft',
            count: statusCounts[ListingStatus.draft] ?? 0,
            isSelected: selectedStatus == ListingStatus.draft,
            onTap: () => onStatusChanged(ListingStatus.draft),
            color: AppColors.textSecondary,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            label: 'Active',
            count: statusCounts[ListingStatus.active] ?? 0,
            isSelected: selectedStatus == ListingStatus.active,
            onTap: () => onStatusChanged(ListingStatus.active),
            color: AppColors.success,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            label: 'Pending',
            count: statusCounts[ListingStatus.pendingReview] ?? 0,
            isSelected: selectedStatus == ListingStatus.pendingReview,
            onTap: () => onStatusChanged(ListingStatus.pendingReview),
            color: AppColors.warning,
          ),
          const SizedBox(width: 8),
          _buildFilterChip(
            label: 'Rejected',
            count: statusCounts[ListingStatus.rejected] ?? 0,
            isSelected: selectedStatus == ListingStatus.rejected,
            onTap: () => onStatusChanged(ListingStatus.rejected),
            color: AppColors.error,
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required int count,
    required bool isSelected,
    required VoidCallback onTap,
    Color? color,
  }) {
    final chipColor = color ?? AppColors.primary;
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected 
              ? chipColor 
              : chipColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? chipColor 
                : chipColor.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected 
                    ? AppColors.textOnPrimary 
                    : chipColor,
                fontWeight: isSelected 
                    ? FontWeight.w600 
                    : FontWeight.w500,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.textOnPrimary.withValues(alpha: 0.2)
                      : chipColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  count.toString(),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: isSelected 
                        ? AppColors.textOnPrimary 
                        : chipColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
