import 'package:flutter/foundation.dart';
import '../../data/repositories/listing_repository.dart';
import '../../data/models/listing_model.dart';


enum ListingState {
  initial,
  loading,
  success,
  error,
  creating,
  updating,
  uploading,
  autoSaving
}

class ListingProvider extends ChangeNotifier {
  final ListingRepository _repository;
  
  // State management
  ListingState _state = ListingState.initial;
  String? _errorMessage;
  
  // Listing data
  List<Listing> _listings = [];
  Listing? _currentListing;
  
  // Creation flow tracking
  int _currentStep = 0;
  final int _totalSteps = 4; // basic, amenities, pricing, photos
  bool _isAutoSaving = false;

  ListingProvider({
    required ListingRepository repository,
  }) : _repository = repository;

  // Getters
  ListingState get state => _state;
  String? get errorMessage => _errorMessage;
  List<Listing> get listings => _listings;
  Listing? get currentListing => _currentListing;
  bool get isLoading => _state == ListingState.loading;
  bool get isCreating => _state == ListingState.creating;
  bool get isAutoSaving => _isAutoSaving;
  int get currentStep => _currentStep;
  int get totalSteps => _totalSteps;
  double get progress => _currentStep / _totalSteps;

  // Creation flow methods
  Future<bool> createInitialListing(Map<String, dynamic> initialData) async {
    try {
      _setState(ListingState.creating);
      _clearError();

      final listing = await _repository.createInitialListing(initialData);
      _currentListing = listing;
      _currentStep = 1;
      
      _setState(ListingState.success);
      return true;
    } catch (e) {
      _setError('Failed to create listing: $e');
      return false;
    }
  }

  Future<bool> updateBasicInfo(Map<String, dynamic> basicInfo) async {
    if (_currentListing == null) {
      _setError('No active listing');
      return false;
    }

    try {
      _setState(ListingState.updating);
      _clearError();

      final updated = await _repository.updateBasicInfo(
        _currentListing!.listingId,
        basicInfo
      );
      _currentListing = updated;
      _moveToNextStep();
      
      _setState(ListingState.success);
      return true;
    } catch (e) {
      _setError('Failed to update basic info: $e');
      return false;
    }
  }

  Future<bool> updateAmenities(List<String> amenities) async {
    if (_currentListing == null) {
      _setError('No active listing');
      return false;
    }

    try {
      _setState(ListingState.updating);
      _clearError();

      final updated = await _repository.updateAmenities(
        _currentListing!.listingId,
        amenities
      );
      _currentListing = updated;
      _moveToNextStep();
      
      _setState(ListingState.success);
      return true;
    } catch (e) {
      _setError('Failed to update amenities: $e');
      return false;
    }
  }

  Future<bool> updatePricingPolicy(Map<String, dynamic> pricingData) async {
    if (_currentListing == null) {
      _setError('No active listing');
      return false;
    }

    try {
      _setState(ListingState.updating);
      _clearError();

      final updated = await _repository.updatePricingPolicy(
        _currentListing!.listingId,
        pricingData
      );
      _currentListing = updated;
      _moveToNextStep();
      
      _setState(ListingState.success);
      return true;
    } catch (e) {
      _setError('Failed to update pricing: $e');
      return false;
    }
  }

  // Navigation helpers
  void _moveToNextStep() {
    if (_currentStep < _totalSteps - 1) {
      _currentStep++;
      notifyListeners();
    }
  }

  void goToStep(int step) {
    if (step >= 0 && step < _totalSteps) {
      _currentStep = step;
      notifyListeners();
    }
  }

  // Progress validation
  bool canProceedToNext() {
    if (_currentListing == null) return false;
    
    switch (_currentStep) {
      case 0:
        return _currentListing!.hasBasicInfo;
      case 1:
        return _currentListing!.amenities?.isNotEmpty ?? false;
      case 2:
        return _currentListing!.hasPricing;
      case 3:
        return _currentListing!.hasPhotos;
      default:
        return false;
    }
  }

  bool get canSubmitForReview => 
    _currentListing?.isReadyForSubmission ?? false;

  // State management helpers
  void _setState(ListingState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setError(String message) {
    _errorMessage = message;
    _setState(ListingState.error);
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Reset provider state
  void reset() {
    _state = ListingState.initial;
    _errorMessage = null;
    _currentListing = null;
    _currentStep = 0;
    _isAutoSaving = false;
    notifyListeners();
  }
}