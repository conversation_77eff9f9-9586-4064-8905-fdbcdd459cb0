import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';

class CreateListingLoadingScreen extends StatefulWidget {
  const CreateListingLoadingScreen({super.key});

  @override
  State<CreateListingLoadingScreen> createState() => _CreateListingLoadingScreenState();
}

class _CreateListingLoadingScreenState extends State<CreateListingLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _mascotController;
  late AnimationController _textController;
  
  late Animation<double> _progressAnimation;
  late Animation<double> _mascotBounceAnimation;
  late Animation<double> _textFadeAnimation;

  final List<String> _loadingMessages = [
    'Setting up your new listing...',
    'Preparing the workspace...',
    'Almost ready...',
    'Let\'s get started!',
  ];

  int _currentMessageIndex = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startLoadingSequence();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _mascotController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _textController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _mascotBounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mascotController,
      curve: Curves.elasticOut,
    ));

    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));
  }

  void _startLoadingSequence() async {
    // Start animations
    _mascotController.repeat(reverse: true);
    _textController.forward();
    
    // Cycle through messages
    for (int i = 0; i < _loadingMessages.length; i++) {
      setState(() => _currentMessageIndex = i);
      
      if (i == 0) {
        _progressController.animateTo(0.3);
      } else if (i == 1) {
        _progressController.animateTo(0.6);
      } else if (i == 2) {
        _progressController.animateTo(0.9);
      } else {
        _progressController.animateTo(1.0);
      }
      
      await Future.delayed(const Duration(milliseconds: 800));
      
      if (i < _loadingMessages.length - 1) {
        await _textController.reverse();
        await Future.delayed(const Duration(milliseconds: 100));
        await _textController.forward();
      }
    }

    // Complete loading
    await Future.delayed(const Duration(milliseconds: 500));
    
    if (mounted) {
      // Navigate to basic property info screen
      context.go('/host/listings/create/basic-info');
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _mascotController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              
              // Animated mascot
              _buildAnimatedMascot(),
              const SizedBox(height: 48),
              
              // Loading message
              _buildLoadingMessage(),
              const SizedBox(height: 32),
              
              // Progress bar
              _buildProgressBar(),
              const SizedBox(height: 16),
              
              // Progress text
              _buildProgressText(),
              
              const Spacer(),
              
              // Skip button
              _buildSkipButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedMascot() {
    return AnimatedBuilder(
      animation: _mascotBounceAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -10 * _mascotBounceAnimation.value),
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary,
                  AppColors.primary.withValues(alpha: 0.8),
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: const Icon(
              Icons.home_work,
              size: 60,
              color: AppColors.textOnPrimary,
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingMessage() {
    return AnimatedBuilder(
      animation: _textFadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _textFadeAnimation.value,
          child: Text(
            _loadingMessages[_currentMessageIndex],
            style: AppTextStyles.headlineMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
        );
      },
    );
  }

  Widget _buildProgressBar() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Column(
          children: [
            Container(
              width: double.infinity,
              height: 8,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(4),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4),
                child: LinearProgressIndicator(
                  value: _progressAnimation.value,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation(AppColors.primary),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProgressText() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        final percentage = (_progressAnimation.value * 100).round();
        return Text(
          '$percentage% Complete',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        );
      },
    );
  }

  Widget _buildSkipButton() {
    return TextButton(
      onPressed: () {
        context.go('/host/listings/create/basic-info');
      },
      child: Text(
        'Skip Animation',
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.textSecondary,
        ),
      ),
    );
  }
}
