import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/progress_stepper.dart';
import '../widgets/listing_preview_card.dart';
import '../../data/models/listing_model.dart';

class PreviewReviewScreen extends StatefulWidget {
  final String listingId;

  const PreviewReviewScreen({
    super.key,
    required this.listingId,
  });

  @override
  State<PreviewReviewScreen> createState() => _PreviewReviewScreenState();
}

class _PreviewReviewScreenState extends State<PreviewReviewScreen> {
  bool _isLoading = false;
  bool _isSubmitting = false;
  Listing? _listing;

  @override
  void initState() {
    super.initState();
    _loadListingData();
  }

  Future<void> _loadListingData() async {
    setState(() => _isLoading = true);
    await context.read<ListingProvider>().loadListing(widget.listingId);
    
    final listing = context.read<ListingProvider>().currentListing;
    if (listing != null) {
      setState(() => _listing = listing);
    }
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            const ProgressStepper(currentStep: 8, totalSteps: 9),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Preview & Review',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Review your listing before publishing',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_listing == null) {
      return const Center(
        child: Text('Failed to load listing data'),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Preview card
          Text(
            'How your listing will appear to guests:',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ListingPreviewCard(listing: _listing!),
          const SizedBox(height: 32),

          // Completion checklist
          _buildCompletionChecklist(),
          const SizedBox(height: 32),

          // Publishing info
          _buildPublishingInfo(),
        ],
      ),
    );
  }

  Widget _buildCompletionChecklist() {
    final checklist = [
      {
        'title': 'Basic Information',
        'completed': _listing!.title.isNotEmpty && _listing!.description.isNotEmpty,
        'description': 'Property title, description, and details',
      },
      {
        'title': 'Location',
        'completed': _listing!.address.street.isNotEmpty && _listing!.address.city.isNotEmpty,
        'description': 'Property address and location',
      },
      {
        'title': 'Amenities',
        'completed': _listing!.amenities.isNotEmpty,
        'description': 'Property features and amenities',
      },
      {
        'title': 'Pricing',
        'completed': _listing!.pricing.basePrice > 0,
        'description': 'Base price and policies',
      },
      {
        'title': 'Photos',
        'completed': _listing!.photos.isNotEmpty,
        'description': 'Property photos (${_listing!.photos.length} uploaded)',
      },
    ];

    final completedCount = checklist.where((item) => item['completed'] as bool).length;
    final isComplete = completedCount == checklist.length;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isComplete ? AppColors.success : AppColors.warning,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isComplete ? Icons.check_circle : Icons.warning,
                color: isComplete ? AppColors.success : AppColors.warning,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Listing Completion',
                style: AppTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              Text(
                '$completedCount/${checklist.length}',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isComplete ? AppColors.success : AppColors.warning,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...checklist.map((item) {
            final completed = item['completed'] as bool;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Icon(
                    completed ? Icons.check_circle : Icons.radio_button_unchecked,
                    color: completed ? AppColors.success : AppColors.textSecondary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['title'] as String,
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: completed ? AppColors.textPrimary : AppColors.textSecondary,
                          ),
                        ),
                        Text(
                          item['description'] as String,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildPublishingInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.primary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.publish,
                color: AppColors.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Ready to Publish?',
                style: AppTextStyles.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoItem(
            'Your listing will be reviewed by our team',
            Icons.verified_user,
          ),
          _buildInfoItem(
            'Review typically takes 24-48 hours',
            Icons.schedule,
          ),
          _buildInfoItem(
            'You\'ll be notified once it\'s approved',
            Icons.notifications,
          ),
          _buildInfoItem(
            'You can edit your listing anytime',
            Icons.edit,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    final isComplete = _isListingComplete();
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Back to Edit'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: isComplete && !_isSubmitting ? _submitForReview : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(AppColors.textOnPrimary),
                      ),
                    )
                  : const Text('Submit for Review'),
            ),
          ),
        ],
      ),
    );
  }

  bool _isListingComplete() {
    if (_listing == null) return false;
    
    return _listing!.title.isNotEmpty &&
           _listing!.description.isNotEmpty &&
           _listing!.address.street.isNotEmpty &&
           _listing!.address.city.isNotEmpty &&
           _listing!.amenities.isNotEmpty &&
           _listing!.pricing.basePrice > 0 &&
           _listing!.photos.isNotEmpty;
  }

  Future<void> _submitForReview() async {
    setState(() => _isSubmitting = true);
    
    try {
      final success = await context.read<ListingProvider>().submitForReview(widget.listingId);
      
      if (success && mounted) {
        // Navigate to success screen
        context.go('/host/listings/${widget.listingId}/publish-success');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit listing: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isSubmitting = false);
    }
  }
}
