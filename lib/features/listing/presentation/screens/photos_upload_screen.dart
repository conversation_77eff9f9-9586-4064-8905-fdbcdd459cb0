import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/progress_stepper.dart';
import '../widgets/photo_upload_widget.dart';

class PhotosUploadScreen extends StatefulWidget {
  final String listingId;

  const PhotosUploadScreen({
    super.key,
    required this.listingId,
  });

  @override
  State<PhotosUploadScreen> createState() => _PhotosUploadScreenState();
}

class _PhotosUploadScreenState extends State<PhotosUploadScreen> {
  List<String> _uploadedPhotos = [];
  bool _isLoading = false;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    _loadListingData();
  }

  Future<void> _loadListingData() async {
    setState(() => _isLoading = true);
    await context.read<ListingProvider>().loadListing(widget.listingId);
    
    final listing = context.read<ListingProvider>().currentListing;
    if (listing != null) {
      setState(() {
        _uploadedPhotos = List.from(listing.photos);
      });
    }
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            const ProgressStepper(currentStep: 3, totalSteps: 9),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Photos & Media',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Add photos to showcase your property',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Upload status
          if (_uploadedPhotos.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppColors.success,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '${_uploadedPhotos.length} photos uploaded',
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 24),

          // Upload guidelines
          _buildUploadGuidelines(),
          const SizedBox(height: 24),

          // Photo upload widget
          PhotoUploadWidget(
            photos: _uploadedPhotos,
            onPhotosSelected: _handlePhotosSelected,
            onPhotoRemoved: _handlePhotoRemoved,
            onPhotoReordered: _handlePhotoReordered,
            isUploading: _isUploading,
          ),
          const SizedBox(height: 24),

          // Tips section
          _buildPhotoTips(),
        ],
      ),
    );
  }

  Widget _buildUploadGuidelines() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Photo Guidelines',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildGuidelineItem('Add at least 5 high-quality photos'),
          _buildGuidelineItem('First photo will be your cover image'),
          _buildGuidelineItem('Show all rooms and key features'),
          _buildGuidelineItem('Use good lighting and clear angles'),
          _buildGuidelineItem('Maximum file size: 10MB per photo'),
        ],
      ),
    );
  }

  Widget _buildGuidelineItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            Icons.check,
            color: AppColors.primary,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoTips() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📸 Pro Tips for Great Photos',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          _buildTipItem('Take photos during the day with natural light'),
          _buildTipItem('Clean and declutter spaces before shooting'),
          _buildTipItem('Show the view from windows if it\'s nice'),
          _buildTipItem('Include photos of amenities like kitchen, bathroom'),
          _buildTipItem('Take wide-angle shots to show space size'),
        ],
      ),
    );
  }

  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 6),
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Back'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canContinue() ? _continue : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isUploading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(AppColors.textOnPrimary),
                      ),
                    )
                  : const Text('Continue'),
            ),
          ),
        ],
      ),
    );
  }

  bool _canContinue() {
    return _uploadedPhotos.isNotEmpty && !_isUploading;
  }

  Future<void> _handlePhotosSelected(List<String> photoPaths) async {
    setState(() => _isUploading = true);
    
    try {
      final uploadedUrls = await context.read<ListingProvider>().uploadPhotos(
        widget.listingId,
        photoPaths,
      );
      
      if (uploadedUrls != null) {
        setState(() {
          _uploadedPhotos.addAll(uploadedUrls);
        });
        
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${uploadedUrls.length} photos uploaded successfully!'),
              backgroundColor: AppColors.success,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload photos: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      setState(() => _isUploading = false);
    }
  }

  void _handlePhotoRemoved(int index) {
    setState(() {
      _uploadedPhotos.removeAt(index);
    });
    _autoSave();
  }

  void _handlePhotoReordered(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final photo = _uploadedPhotos.removeAt(oldIndex);
      _uploadedPhotos.insert(newIndex, photo);
    });
    _autoSave();
  }

  void _autoSave() {
    final updates = {
      'photos': _uploadedPhotos,
    };
    context.read<ListingProvider>().autoSave(widget.listingId, updates);
  }

  Future<void> _continue() async {
    final updates = {
      'photos': _uploadedPhotos,
    };
    
    final success = await context.read<ListingProvider>().updateListing(
      widget.listingId,
      updates,
    );

    if (success && mounted) {
      context.push('/host/listings/create/${widget.listingId}/availability');
    }
  }
}
