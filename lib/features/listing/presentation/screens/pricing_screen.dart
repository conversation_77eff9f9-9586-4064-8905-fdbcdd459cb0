import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/progress_stepper.dart';
import '../../data/models/listing_model.dart';

class PricingScreen extends StatefulWidget {
  final String listingId;

  const PricingScreen({
    super.key,
    required this.listingId,
  });

  @override
  State<PricingScreen> createState() => _PricingScreenState();
}

class _PricingScreenState extends State<PricingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _basePriceController = TextEditingController();
  final _cleaningFeeController = TextEditingController();
  final _serviceFeeController = TextEditingController();
  final _minStayController = TextEditingController(text: '1');
  final _maxStayController = TextEditingController(text: '30');

  CancellationPolicy _selectedPolicy = CancellationPolicy.moderate;
  String _selectedCurrency = 'TSH';
  bool _isLoading = false;

  final List<String> _currencies = ['TSH', 'USD', 'EUR', 'KES', 'UGX'];

  @override
  void initState() {
    super.initState();
    _loadListingData();
  }

  @override
  void dispose() {
    _basePriceController.dispose();
    _cleaningFeeController.dispose();
    _serviceFeeController.dispose();
    _minStayController.dispose();
    _maxStayController.dispose();
    super.dispose();
  }

  Future<void> _loadListingData() async {
    setState(() => _isLoading = true);
    await context.read<ListingProvider>().loadListing(widget.listingId);
    
    final listing = context.read<ListingProvider>().currentListing;
    if (listing != null) {
      _populateFields(listing);
    }
    setState(() => _isLoading = false);
  }

  void _populateFields(Listing listing) {
    _basePriceController.text = listing.pricing.basePrice.toString();
    _cleaningFeeController.text = listing.pricing.cleaningFee?.toString() ?? '';
    _serviceFeeController.text = listing.pricing.serviceFee?.toString() ?? '';
    _minStayController.text = listing.pricing.minStayNights.toString();
    _maxStayController.text = listing.pricing.maxStayNights.toString();
    _selectedPolicy = listing.pricing.cancellationPolicy;
    _selectedCurrency = listing.pricing.currency;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            const ProgressStepper(currentStep: 2, totalSteps: 9),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildForm(),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Pricing & Policies',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Set your pricing and booking policies',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Base Price
            _buildSectionTitle('Base Price'),
            Row(
              children: [
                SizedBox(
                  width: 100,
                  child: DropdownButtonFormField<String>(
                    value: _selectedCurrency,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: AppColors.surface,
                    ),
                    items: _currencies.map((currency) {
                      return DropdownMenuItem(
                        value: currency,
                        child: Text(currency),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedCurrency = value!);
                      _autoSave();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildPriceField(
                    controller: _basePriceController,
                    label: 'Price per night',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final price = double.tryParse(value!);
                      if (price == null || price <= 0) {
                        return 'Invalid price';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Additional Fees
            _buildSectionTitle('Additional Fees (Optional)'),
            _buildPriceField(
              controller: _cleaningFeeController,
              label: 'Cleaning fee',
              hint: 'One-time fee for cleaning',
            ),
            const SizedBox(height: 16),
            _buildPriceField(
              controller: _serviceFeeController,
              label: 'Service fee',
              hint: 'Additional service charges',
            ),
            const SizedBox(height: 24),

            // Stay Duration
            _buildSectionTitle('Stay Duration'),
            Row(
              children: [
                Expanded(
                  child: _buildNumberField(
                    controller: _minStayController,
                    label: 'Minimum nights',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final nights = int.tryParse(value!);
                      if (nights == null || nights < 1) {
                        return 'Min 1 night';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildNumberField(
                    controller: _maxStayController,
                    label: 'Maximum nights',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final nights = int.tryParse(value!);
                      final minNights = int.tryParse(_minStayController.text) ?? 1;
                      if (nights == null || nights < minNights) {
                        return 'Must be ≥ min nights';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Cancellation Policy
            _buildSectionTitle('Cancellation Policy'),
            _buildCancellationPolicySelector(),
            const SizedBox(height: 24),

            // Price Summary
            _buildPriceSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: AppTextStyles.headlineSmall.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildPriceField({
    required TextEditingController controller,
    required String label,
    String? hint,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      validator: validator,
      onChanged: (_) => _autoSave(),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
      ),
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: validator,
      onChanged: (_) => _autoSave(),
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
      ),
    );
  }

  Widget _buildCancellationPolicySelector() {
    final policies = [
      {
        'policy': CancellationPolicy.flexible,
        'title': 'Flexible',
        'description': 'Full refund 1 day prior to arrival',
      },
      {
        'policy': CancellationPolicy.moderate,
        'title': 'Moderate',
        'description': 'Full refund 5 days prior to arrival',
      },
      {
        'policy': CancellationPolicy.strict,
        'title': 'Strict',
        'description': '50% refund up until 1 week prior to arrival',
      },
      {
        'policy': CancellationPolicy.superStrict,
        'title': 'Super Strict',
        'description': '50% refund up until 30 days prior to arrival',
      },
    ];

    return Column(
      children: policies.map((policy) {
        final policyType = policy['policy'] as CancellationPolicy;
        final isSelected = _selectedPolicy == policyType;
        
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: GestureDetector(
            onTap: () {
              setState(() => _selectedPolicy = policyType);
              _autoSave();
            },
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isSelected 
                    ? AppColors.primary.withValues(alpha: 0.1) 
                    : AppColors.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected ? AppColors.primary : AppColors.border,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          policy['title'] as String,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.w600,
                            color: isSelected ? AppColors.primary : AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          policy['description'] as String,
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(
                      Icons.check_circle,
                      color: AppColors.primary,
                      size: 24,
                    )
                  else
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.border, width: 2),
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPriceSummary() {
    final basePrice = double.tryParse(_basePriceController.text) ?? 0;
    final cleaningFee = double.tryParse(_cleaningFeeController.text) ?? 0;
    final serviceFee = double.tryParse(_serviceFeeController.text) ?? 0;
    final totalPerNight = basePrice + serviceFee;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Price Summary',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 12),
          _buildPriceRow('Base price', basePrice),
          if (serviceFee > 0) _buildPriceRow('Service fee', serviceFee),
          if (cleaningFee > 0) _buildPriceRow('Cleaning fee (one-time)', cleaningFee),
          const Divider(),
          _buildPriceRow('Total per night', totalPerNight, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildPriceRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '$_selectedCurrency ${amount.toStringAsFixed(0)}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Back'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canContinue() ? _continue : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Continue'),
            ),
          ),
        ],
      ),
    );
  }

  bool _canContinue() {
    return _basePriceController.text.isNotEmpty &&
           _minStayController.text.isNotEmpty &&
           _maxStayController.text.isNotEmpty;
  }

  void _autoSave() {
    final updates = _buildUpdateData();
    context.read<ListingProvider>().autoSave(widget.listingId, updates);
  }

  Map<String, dynamic> _buildUpdateData() {
    return {
      'pricing': {
        'base_price': double.tryParse(_basePriceController.text) ?? 0,
        'currency': _selectedCurrency,
        'cleaning_fee': double.tryParse(_cleaningFeeController.text),
        'service_fee': double.tryParse(_serviceFeeController.text),
        'cancellation_policy': _selectedPolicy.name,
        'min_stay_nights': int.tryParse(_minStayController.text) ?? 1,
        'max_stay_nights': int.tryParse(_maxStayController.text) ?? 30,
      },
    };
  }

  Future<void> _continue() async {
    if (_formKey.currentState?.validate() ?? false) {
      final updates = _buildUpdateData();
      final success = await context.read<ListingProvider>().updateListing(
        widget.listingId,
        updates,
      );

      if (success && mounted) {
        context.push('/host/listings/create/${widget.listingId}/photos');
      }
    }
  }
}
