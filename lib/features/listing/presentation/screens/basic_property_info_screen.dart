import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/progress_stepper.dart';
import '../widgets/property_type_selector.dart';
import '../../data/models/listing_model.dart';

class BasicPropertyInfoScreen extends StatefulWidget {
  final String? listingId;

  const BasicPropertyInfoScreen({
    super.key,
    this.listingId,
  });

  @override
  State<BasicPropertyInfoScreen> createState() => _BasicPropertyInfoScreenState();
}

class _BasicPropertyInfoScreenState extends State<BasicPropertyInfoScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _streetController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _bedroomsController = TextEditingController();
  final _bathroomsController = TextEditingController();
  final _maxGuestsController = TextEditingController();

  PropertyType _selectedPropertyType = PropertyType.apartment;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadListingData();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _streetController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _postalCodeController.dispose();
    _bedroomsController.dispose();
    _bathroomsController.dispose();
    _maxGuestsController.dispose();
    super.dispose();
  }

  Future<void> _loadListingData() async {
    if (widget.listingId != null) {
      setState(() => _isLoading = true);
      await context.read<ListingProvider>().loadListing(widget.listingId!);
      
      final listing = context.read<ListingProvider>().currentListing;
      if (listing != null) {
        _populateFields(listing);
      }
      setState(() => _isLoading = false);
    }
  }

  void _populateFields(Listing listing) {
    _titleController.text = listing.title;
    _descriptionController.text = listing.description;
    _streetController.text = listing.address.street;
    _cityController.text = listing.address.city;
    _stateController.text = listing.address.state;
    _postalCodeController.text = listing.address.postalCode;
    _bedroomsController.text = listing.details.bedrooms.toString();
    _bathroomsController.text = listing.details.bathrooms.toString();
    _maxGuestsController.text = listing.details.maxGuests.toString();
    _selectedPropertyType = listing.propertyType;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            const ProgressStepper(currentStep: 0, totalSteps: 9),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildForm(),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Basic Property Information',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Tell us about your property to get started',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Type
            _buildSectionTitle('Property Type'),
            PropertyTypeSelector(
              selectedType: _selectedPropertyType,
              onTypeSelected: (type) {
                setState(() => _selectedPropertyType = type);
                _autoSave();
              },
            ),
            const SizedBox(height: 24),

            // Basic Info
            _buildSectionTitle('Basic Information'),
            _buildTextField(
              controller: _titleController,
              label: 'Property Title',
              hint: 'e.g., Cozy 2BR Apartment in City Center',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter a property title';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _descriptionController,
              label: 'Description',
              hint: 'Describe your property, amenities, and what makes it special',
              maxLines: 4,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Address
            _buildSectionTitle('Address'),
            _buildTextField(
              controller: _streetController,
              label: 'Street Address',
              hint: 'e.g., 123 Main Street',
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'Please enter street address';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildTextField(
                    controller: _cityController,
                    label: 'City',
                    hint: 'e.g., Dar es Salaam',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTextField(
                    controller: _stateController,
                    label: 'State/Region',
                    hint: 'e.g., Dar es Salaam',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTextField(
              controller: _postalCodeController,
              label: 'Postal Code',
              hint: 'e.g., 12345',
            ),
            const SizedBox(height: 24),

            // Property Details
            _buildSectionTitle('Property Details'),
            Row(
              children: [
                Expanded(
                  child: _buildNumberField(
                    controller: _bedroomsController,
                    label: 'Bedrooms',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final number = int.tryParse(value!);
                      if (number == null || number < 0) {
                        return 'Invalid';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildNumberField(
                    controller: _bathroomsController,
                    label: 'Bathrooms',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final number = int.tryParse(value!);
                      if (number == null || number < 0) {
                        return 'Invalid';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildNumberField(
                    controller: _maxGuestsController,
                    label: 'Max Guests',
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Required';
                      }
                      final number = int.tryParse(value!);
                      if (number == null || number < 1) {
                        return 'Min 1';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: AppTextStyles.headlineSmall.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      validator: validator,
      onChanged: (_) => _autoSave(),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
      ),
    );
  }

  Widget _buildNumberField({
    required TextEditingController controller,
    required String label,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.number,
      validator: validator,
      onChanged: (_) => _autoSave(),
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.primary, width: 2),
        ),
        filled: true,
        fillColor: AppColors.surface,
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Save as Draft'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _canContinue() ? _continue : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Continue'),
            ),
          ),
        ],
      ),
    );
  }

  bool _canContinue() {
    return _titleController.text.isNotEmpty &&
           _descriptionController.text.isNotEmpty &&
           _streetController.text.isNotEmpty &&
           _cityController.text.isNotEmpty &&
           _bedroomsController.text.isNotEmpty &&
           _bathroomsController.text.isNotEmpty &&
           _maxGuestsController.text.isNotEmpty;
  }

  void _autoSave() {
    if (widget.listingId != null) {
      final updates = _buildUpdateData();
      context.read<ListingProvider>().autoSave(widget.listingId!, updates);
    }
  }

  Map<String, dynamic> _buildUpdateData() {
    return {
      'title': _titleController.text,
      'description': _descriptionController.text,
      'property_type': _selectedPropertyType.name,
      'address': {
        'street': _streetController.text,
        'city': _cityController.text,
        'state': _stateController.text,
        'postal_code': _postalCodeController.text,
        'country': 'Tanzania', // Default for now
        'latitude': 0.0, // Will be set via map picker later
        'longitude': 0.0,
      },
      'details': {
        'bedrooms': int.tryParse(_bedroomsController.text) ?? 0,
        'bathrooms': int.tryParse(_bathroomsController.text) ?? 0,
        'max_guests': int.tryParse(_maxGuestsController.text) ?? 1,
        'check_in_time': '15:00',
        'check_out_time': '11:00',
      },
    };
  }

  Future<void> _continue() async {
    if (_formKey.currentState?.validate() ?? false) {
      final updates = _buildUpdateData();
      final success = await context.read<ListingProvider>().updateListing(
        widget.listingId!,
        updates,
      );

      if (success && mounted) {
        context.push('/host/listings/create/${widget.listingId}/amenities');
      }
    }
  }
}
