import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/progress_stepper.dart';


class AmenitiesScreen extends StatefulWidget {
  final String listingId;

  const AmenitiesScreen({
    super.key,
    required this.listingId,
  });

  @override
  State<AmenitiesScreen> createState() => _AmenitiesScreenState();
}

class _AmenitiesScreenState extends State<AmenitiesScreen> {
  List<String> _selectedAmenities = [];
  bool _isLoading = false;

  final List<Map<String, dynamic>> _amenityCategories = [
    {
      'title': 'Essential',
      'amenities': [
        {'id': 'wifi', 'name': 'Wi-Fi', 'icon': Icons.wifi},
        {'id': 'kitchen', 'name': 'Kitchen', 'icon': Icons.kitchen},
        {'id': 'parking', 'name': 'Free parking', 'icon': Icons.local_parking},
        {'id': 'air_conditioning', 'name': 'Air conditioning', 'icon': Icons.ac_unit},
        {'id': 'heating', 'name': 'Heating', 'icon': Icons.thermostat},
        {'id': 'hot_water', 'name': 'Hot water', 'icon': Icons.hot_tub},
      ],
    },
    {
      'title': 'Features',
      'amenities': [
        {'id': 'tv', 'name': 'TV', 'icon': Icons.tv},
        {'id': 'washing_machine', 'name': 'Washing machine', 'icon': Icons.local_laundry_service},
        {'id': 'dryer', 'name': 'Dryer', 'icon': Icons.dry_cleaning},
        {'id': 'iron', 'name': 'Iron', 'icon': Icons.iron},
        {'id': 'hair_dryer', 'name': 'Hair dryer', 'icon': Icons.dry},
        {'id': 'safe', 'name': 'Safe', 'icon': Icons.security},
      ],
    },
    {
      'title': 'Location',
      'amenities': [
        {'id': 'beach_access', 'name': 'Beach access', 'icon': Icons.beach_access},
        {'id': 'city_center', 'name': 'City center', 'icon': Icons.location_city},
        {'id': 'public_transport', 'name': 'Public transport', 'icon': Icons.directions_bus},
        {'id': 'airport_shuttle', 'name': 'Airport shuttle', 'icon': Icons.airport_shuttle},
        {'id': 'gym', 'name': 'Gym', 'icon': Icons.fitness_center},
        {'id': 'pool', 'name': 'Pool', 'icon': Icons.pool},
      ],
    },
    {
      'title': 'Safety',
      'amenities': [
        {'id': 'smoke_alarm', 'name': 'Smoke alarm', 'icon': Icons.smoke_free},
        {'id': 'carbon_monoxide_alarm', 'name': 'Carbon monoxide alarm', 'icon': Icons.warning},
        {'id': 'fire_extinguisher', 'name': 'Fire extinguisher', 'icon': Icons.fire_extinguisher},
        {'id': 'first_aid_kit', 'name': 'First aid kit', 'icon': Icons.medical_services},
        {'id': 'security_cameras', 'name': 'Security cameras', 'icon': Icons.security},
        {'id': 'lockbox', 'name': 'Lockbox', 'icon': Icons.lock},
      ],
    },
    {
      'title': 'Accessibility',
      'amenities': [
        {'id': 'wheelchair_accessible', 'name': 'Wheelchair accessible', 'icon': Icons.accessible},
        {'id': 'step_free_access', 'name': 'Step-free access', 'icon': Icons.accessible_forward},
        {'id': 'wide_doorways', 'name': 'Wide doorways', 'icon': Icons.door_front_door},
        {'id': 'accessible_bathroom', 'name': 'Accessible bathroom', 'icon': Icons.wc},
      ],
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadListingData();
  }

  Future<void> _loadListingData() async {
    setState(() => _isLoading = true);
    await context.read<ListingProvider>().loadListing(widget.listingId);
    
    final listing = context.read<ListingProvider>().currentListing;
    if (listing != null) {
      setState(() {
        _selectedAmenities = List.from(listing.amenities);
      });
    }
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            const ProgressStepper(currentStep: 1, totalSteps: 9),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Amenities & Features',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'What amenities do you offer? Select all that apply',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Selected count
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppColors.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  '${_selectedAmenities.length} amenities selected',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Amenity categories
          ..._amenityCategories.map((category) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCategoryTitle(category['title'] as String),
                const SizedBox(height: 12),
                _buildAmenityGrid(category['amenities'] as List<Map<String, dynamic>>),
                const SizedBox(height: 24),
              ],
            );
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildCategoryTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.headlineSmall.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildAmenityGrid(List<Map<String, dynamic>> amenities) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: amenities.length,
      itemBuilder: (context, index) {
        final amenity = amenities[index];
        final isSelected = _selectedAmenities.contains(amenity['id']);
        
        return GestureDetector(
          onTap: () => _toggleAmenity(amenity['id'] as String),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: isSelected 
                  ? AppColors.primary.withValues(alpha: 0.1) 
                  : AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? AppColors.primary : AppColors.border,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  amenity['icon'] as IconData,
                  color: isSelected ? AppColors.primary : AppColors.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    amenity['name'] as String,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: isSelected ? AppColors.primary : AppColors.textPrimary,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: AppColors.primary,
                    size: 16,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => context.pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Back'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _continue,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.textOnPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Continue'),
            ),
          ),
        ],
      ),
    );
  }

  void _toggleAmenity(String amenityId) {
    setState(() {
      if (_selectedAmenities.contains(amenityId)) {
        _selectedAmenities.remove(amenityId);
      } else {
        _selectedAmenities.add(amenityId);
      }
    });
    _autoSave();
  }

  void _autoSave() {
    final updates = {
      'amenities': _selectedAmenities,
    };
    context.read<ListingProvider>().autoSave(widget.listingId, updates);
  }

  Future<void> _continue() async {
    final updates = {
      'amenities': _selectedAmenities,
    };
    
    final success = await context.read<ListingProvider>().updateListing(
      widget.listingId,
      updates,
    );

    if (success && mounted) {
      context.push('/host/listings/create/${widget.listingId}/pricing');
    }
  }
}
