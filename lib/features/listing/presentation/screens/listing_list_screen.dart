import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../presentation/providers/listing_provider.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../data/models/listing_model.dart';

class ListingListScreen extends StatefulWidget {
  const ListingListScreen({Key? key}) : super(key: key);

  @override
  State<ListingListScreen> createState() => _ListingListScreenState();
}

class _ListingListScreenState extends State<ListingListScreen> {
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    // Load listings when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ListingProvider>().loadListings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Your Listings',
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _navigateToCreateListing(context),
          ),
        ],
      ),
      body: Consumer<ListingProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const LoadingWidget();
          }

          if (provider.errorMessage != null) {
            return CustomErrorWidget(
              message: provider.errorMessage!,
              onRetry: () => provider.loadListings(),
            );
          }

          if (provider.listnow we have implemented the listing service, listing repository, listing provider and listing model , can you know add the all logic to the listing already defined screens that are current  found in presentation  modify their logic but maintain some important stuffs, to match the logic of data to presentation and makesure the routes also work fines in listing feature ings.isEmpty) {
            return _buildEmptyState();
          }

          return _isGridView
              ? _buildGridView(provider.listings)
              : _buildListView(provider.listings);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.home_work_outlined, size: 64),
          const SizedBox(height: 16),
          const Text(
            'No listings yet',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('Create your first listing to get started'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => _navigateToCreateListing(context),
            child: const Text('Create Listing'),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView(List<Listing> listings) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: listings.length,
      itemBuilder: (context, index) {
        return _ListingCard(
          listing: listings[index],
          onTap: () => _navigateToViewListing(context, listings[index].id),
        );
      },
    );
  }

  Widget _buildListView(List<Listing> listings) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: listings.length,
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        return _ListingListItem(
          listing: listings[index],
          onTap: () => _navigateToViewListing(context, listings[index].id),
        );
      },
    );
  }

  void _navigateToCreateListing(BuildContext context) {
    // Navigate to create listing screen
    context.read<ListingProvider>().reset(); // Reset provider state
    Navigator.pushNamed(context, '/listings/create');
  }

  void _navigateToViewListing(BuildContext context, String listingId) {
    Navigator.pushNamed(
      context,
      '/listings/view/$listingId',
    );
  }
}

class _ListingCard extends StatelessWidget {
  final Listing listing;
  final VoidCallback onTap;

  const _ListingCard({
    Key? key,
    required this.listing,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            AspectRatio(
              aspectRatio: 1.5,
              child: listing.photoUrls.isNotEmpty
                  ? Image.network(
                      listing.photoUrls.first,
                      fit: BoxFit.cover,
                    )
                  : Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.home, size: 40),
                    ),
            ),
            // Details
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    listing.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${listing.currency} ${listing.basePrice}',
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  _buildStatusChip(listing.status),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(ListingStatus status) {
    Color color;
    String label;

    switch (status) {
      case ListingStatus.draft:
        color = Colors.grey;
        label = 'Draft';
        break;
      case ListingStatus.pendingReview:
        color = Colors.orange;
        label = 'Pending Review';
        break;
      case ListingStatus.active:
        color = Colors.green;
        label = 'Active';
        break;
      case ListingStatus.inactive:
        color = Colors.red;
        label = 'Inactive';
        break;
      case ListingStatus.rejected:
        color = Colors.red;
        label = 'Rejected';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}

class _ListingListItem extends StatelessWidget {
  final Listing listing;
  final VoidCallback onTap;

  const _ListingListItem({
    Key? key,
    required this.listing,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              // Thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: SizedBox(
                  width: 80,
                  height: 80,
                  child: listing.photoUrls.isNotEmpty
                      ? Image.network(
                          listing.photoUrls.first,
                          fit: BoxFit.cover,
                        )
                      : Container(
                          color: Colors.grey[300],
                          child: const Icon(Icons.home, size: 40),
                        ),
                ),
              ),
              const SizedBox(width: 16),
              // Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      listing.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      listing.propertyType,
                      style: TextStyle(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${listing.currency} ${listing.basePrice}',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              // Status
              Column(
                children: [
                  _buildStatusChip(listing.status),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(ListingStatus status) {
    Color color;
    String label;

    switch (status) {
      case ListingStatus.draft:
        color = Colors.grey;
        label = 'Draft';
        break;
      case ListingStatus.pendingReview:
        color = Colors.orange;
        label = 'Pending';
        break;
      case ListingStatus.active:
        color = Colors.green;
        label = 'Active';
        break;
      case ListingStatus.inactive:
        color = Colors.red;
        label = 'Inactive';
        break;
      case ListingStatus.rejected:
        color = Colors.red;
        label = 'Rejected';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }
}