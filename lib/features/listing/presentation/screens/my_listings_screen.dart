import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../providers/listing_provider.dart';
import '../widgets/listing_card.dart';

import '../widgets/empty_listings_widget.dart';
import '../../data/models/listing_model.dart';

class MyListingsScreen extends StatefulWidget {
  const MyListingsScreen({super.key});

  @override
  State<MyListingsScreen> createState() => _MyListingsScreenState();
}

class _MyListingsScreenState extends State<MyListingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    // Defer loading until after the build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadListings();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadListings() async {
    setState(() => _isLoading = true);
    await context.read<ListingProvider>().loadListings();
    setState(() => _isLoading = false);
  }

  Future<void> _createNewListing() async {
    final listingProvider = context.read<ListingProvider>();
    final listingId = await listingProvider.createDraftListing();

    if (listingId != null && mounted) {
      context.push('/host/listings/create/$listingId/basic');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: _buildTabBarView(),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildCreateButton(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(
                  Icons.arrow_back,
                  color: AppColors.textOnPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'My Listings',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: AppColors.textOnPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: _loadListings,
                icon: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation(AppColors.textOnPrimary),
                        ),
                      )
                    : const Icon(
                        Icons.refresh,
                        color: AppColors.textOnPrimary,
                      ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Consumer<ListingProvider>(
            builder: (context, provider, child) {
              return Row(
                children: [
                  _buildStatChip(
                    'Total',
                    provider.listings.length.toString(),
                    AppColors.textOnPrimary.withValues(alpha: 0.9),
                  ),
                  const SizedBox(width: 12),
                  _buildStatChip(
                    'Active',
                    provider.activeListings.length.toString(),
                    AppColors.success,
                  ),
                  const SizedBox(width: 12),
                  _buildStatChip(
                    'Pending',
                    provider.pendingListings.length.toString(),
                    AppColors.warning,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            value,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.bodyMedium,
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Draft'),
          Tab(text: 'Active'),
          Tab(text: 'Pending'),
        ],
      ),
    );
  }

  Widget _buildTabBarView() {
    return Consumer<ListingProvider>(
      builder: (context, provider, child) {
        if (provider.state == ListingState.loading && provider.listings.isEmpty) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (provider.state == ListingState.error) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppColors.error.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Failed to load listings',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  provider.errorMessage ?? 'Unknown error occurred',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadListings,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        return TabBarView(
          controller: _tabController,
          children: [
            _buildListingsList(provider.listings),
            _buildListingsList(provider.draftListings),
            _buildListingsList(provider.activeListings),
            _buildListingsList(provider.pendingListings),
          ],
        );
      },
    );
  }

  Widget _buildListingsList(List<Listing> listings) {
    if (listings.isEmpty) {
      return const EmptyListingsWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadListings,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: listings.length,
        itemBuilder: (context, index) {
          final listing = listings[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: ListingCard(
              listing: listing,
              onTap: () => _onListingTap(listing),
              onEdit: () => _onEditListing(listing),
              onDelete: () => _onDeleteListing(listing),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCreateButton() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16, right: 16),
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(28),
        shadowColor: AppColors.primary.withValues(alpha: 0.3),
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: AppColors.primaryGradient,
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: InkWell(
            onTap: _createNewListing,
            borderRadius: BorderRadius.circular(28),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.add_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Create\nListing',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onListingTap(Listing listing) {
    switch (listing.status) {
      case ListingStatus.draft:
        context.push('/host/listings/create/${listing.id}');
        break;
      case ListingStatus.active:
        context.push('/host/listings/${listing.id}');
        break;
      case ListingStatus.pendingReview:
        context.push('/host/listings/${listing.id}/pending');
        break;
      case ListingStatus.rejected:
        context.push('/host/listings/${listing.id}/rejected');
        break;
      default:
        context.push('/host/listings/${listing.id}');
    }
  }

  void _onEditListing(Listing listing) {
    context.push('/host/listings/create/${listing.id}');
  }

  Future<void> _onDeleteListing(Listing listing) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Listing'),
        content: Text('Are you sure you want to delete "${listing.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      await context.read<ListingProvider>().deleteListing(listing.id);
    }
  }
}
