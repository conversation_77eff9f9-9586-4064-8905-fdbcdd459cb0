import 'package:dio/dio.dart';
import '../../../../core/services/http_client.dart';
import '../../../../core/constants/api_constants.dart';

import '../../../../core/utils/logger.dart';
import '../models/listing_model.dart';

class ListingApiService {
  final HttpClient _httpClient;

  ListingApiService({
    HttpClient? httpClient,
  })  : _httpClient = httpClient ?? HttpClient.instance;

  // Create a new listing
    Future<Listing> createListing(Map<String, dynamic> initialData) async {
    try {
      Logger.info('ListingApiService: Creating initial listing');
      final response = await _httpClient.post(
        ApiConstants.createListing,
        data: initialData,
      );
      return Listing.fromJson(response);
    } on DioException catch (e) {
      Logger.error('ListingApiService: Failed to create initial listing', e);
      throw Exception('Failed to create listing: ${e.message}');
    }
  }

  // Step 2: Update basic info
  Future<Listing> updateBasicInfo(String listingId, Map<String, dynamic> basicInfo) async {
    try {
      Logger.info('ListingApiService: Updating basic info for listing $listingId');
      final path = ApiConstants.listingBasicInfo.replaceAll('{id}', listingId);
      final response = await _httpClient.patch(
        path,
        data: basicInfo,
      );
      return Listing.fromJson(response);
    } on DioException catch (e) {
      Logger.error('ListingApiService: Failed to update basic info', e);
      throw Exception('Failed to update basic info: ${e.message}');
    }
  }

  // Step 3: Update amenities
  Future<Listing> updateAmenities(String listingId, List<String> amenities) async {
    try {
      Logger.info('ListingApiService: Updating amenities for listing $listingId');
      final path = ApiConstants.listingAmenities.replaceAll('{id}', listingId);
      final response = await _httpClient.patch(
        path,
        data: {'amenities': amenities},
      );
      return Listing.fromJson(response);
    } on DioException catch (e) {
      Logger.error('ListingApiService: Failed to update amenities', e);
      throw Exception('Failed to update amenities: ${e.message}');
    }
  }

  // Step 4: Update pricing policy
  Future<Listing> updatePricingPolicy(String listingId, Map<String, dynamic> pricingData) async {
    try {
      Logger.info('ListingApiService: Updating pricing for listing $listingId');
      final path = ApiConstants.listingPricePolicy.replaceAll('{id}', listingId);
      final response = await _httpClient.patch(
        path,
        data: pricingData,
      );
      return Listing.fromJson(response);
    } on DioException catch (e) {
      Logger.error('ListingApiService: Failed to update pricing', e);
      throw Exception('Failed to update pricing: ${e.message}');
    }
  }


  // Get all listings for the current host
  // Future<List<Listing>> getHostListings() async {
  //   try {
  //     Logger.info('ListingApiService: Fetching host listings');
  //     final response = await _httpClient.get('/api/listings/host/');
  //     final List<dynamic> listingsJson = response['results'] ?? response['data'] ?? [];
  //     Logger.info('ListingApiService: Fetched ${listingsJson.length} listings');
  //     return listingsJson.map((json) => Listing.fromJson(json)).toList();
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to fetch listings', e);
  //     throw Exception('Failed to fetch listings: ${e.message}');
  //   }
  // }

  // Get a specific listing by ID
  // Future<Listing> getListingById(String listingId) async {
  //   try {
  //     Logger.info('ListingApiService: Fetching listing $listingId');
  //     final response = await _httpClient.get('/api/listings/$listingId/');
  //     Logger.info('ListingApiService: Listing fetched successfully');
  //     return Listing.fromJson(response);
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to fetch listing', e);
  //     throw Exception('Failed to fetch listing: ${e.message}');
  //   }
  // }

  // Update an existing listing
  // Future<Listing> updateListing(String listingId, Map<String, dynamic> updates) async {
  //   try {
  //     Logger.info('ListingApiService: Updating listing $listingId');
  //     final response = await _httpClient.patch(
  //       '/api/listings/$listingId/',
  //       data: updates,
  //     );
  //     Logger.info('ListingApiService: Listing updated successfully');
  //     return Listing.fromJson(response);
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to update listing', e);
  //     throw Exception('Failed to update listing: ${e.message}');
  //   }
  // }

  // Delete a listing
  // Future<void> deleteListing(String listingId) async {
  //   try {
  //     Logger.info('ListingApiService: Deleting listing $listingId');
  //     await _httpClient.delete('/api/listings/$listingId/');
  //     Logger.info('ListingApiService: Listing deleted successfully');
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to delete listing', e);
  //     throw Exception('Failed to delete listing: ${e.message}');
  //   }
  // }

  // Submit listing for review
  // Future<Listing> submitForReview(String listingId) async {
  //   try {
  //     Logger.info('ListingApiService: Submitting listing $listingId for review');
  //     final response = await _httpClient.post(
  //       '/api/listings/$listingId/submit-review/',
  //       data: {},
  //     );
  //     Logger.info('ListingApiService: Listing submitted for review successfully');
  //     return Listing.fromJson(response);
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to submit listing for review', e);
  //     throw Exception('Failed to submit listing for review: ${e.message}');
  //   }
  // }

  // Upload photos for a listing
  // Future<List<String>> uploadPhotos(String listingId, List<String> photoPaths) async {
  //   try {
  //     Logger.info('ListingApiService: Uploading ${photoPaths.length} photos for listing $listingId');
      
  //     final formData = FormData();
  //     for (int i = 0; i < photoPaths.length; i++) {
  //       formData.files.add(MapEntry(
  //         'photos',
  //         await MultipartFile.fromFile(photoPaths[i]),
  //       ));
  //     }

  //     final response = await _httpClient.post(
  //       '/api/listings/$listingId/upload-photos/',
  //       data: formData,
  //     );

  //     final List<dynamic> uploadedUrls = response['photo_urls'] ?? [];
  //     Logger.info('ListingApiService: ${uploadedUrls.length} photos uploaded successfully');
  //     return uploadedUrls.cast<String>();
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to upload photos', e);
  //     throw Exception('Failed to upload photos: ${e.message}');
  //   }
  // }

  // Upload videos for a listing
  // Future<List<String>> uploadVideos(String listingId, List<String> videoPaths) async {
  //   try {
  //     Logger.info('ListingApiService: Uploading ${videoPaths.length} videos for listing $listingId');
      
  //     final formData = FormData();
  //     for (int i = 0; i < videoPaths.length; i++) {
  //       formData.files.add(MapEntry(
  //         'videos',
  //         await MultipartFile.fromFile(videoPaths[i]),
  //       ));
  //     }

  //     final response = await _httpClient.post(
  //       '/api/listings/$listingId/upload-videos/',
  //       data: formData,
  //     );

  //     final List<dynamic> uploadedUrls = response['video_urls'] ?? [];
  //     Logger.info('ListingApiService: ${uploadedUrls.length} videos uploaded successfully');
  //     return uploadedUrls.cast<String>();
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to upload videos', e);
  //     throw Exception('Failed to upload videos: ${e.message}');
  //   }
  // }

  // // Auto-save listing data (draft)
  // Future<void> autoSave(String listingId, Map<String, dynamic> updates) async {
  //   try {
  //     Logger.info('ListingApiService: Auto-saving listing $listingId');
  //     await _httpClient.patch(
  //       '/api/listings/$listingId/auto-save/',
  //       data: updates,
  //     );
  //     Logger.info('ListingApiService: Auto-save completed');
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Auto-save failed', e);
  //     // Don't throw exception for auto-save failures
  //   }
  // }

  // Get listing analytics
  // Future<Map<String, dynamic>> getListingAnalytics(String listingId) async {
  //   try {
  //     Logger.info('ListingApiService: Fetching analytics for listing $listingId');
  //     final response = await _httpClient.get('/api/listings/$listingId/analytics/');
  //     Logger.info('ListingApiService: Analytics fetched successfully');
  //     return response;
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to fetch analytics', e);
  //     throw Exception('Failed to fetch analytics: ${e.message}');
  //   }
  // }

  // Update listing status
  // Future<Listing> updateListingStatus(String listingId, ListingStatus status) async {
  //   try {
  //     Logger.info('ListingApiService: Updating listing $listingId status to ${status.name}');
  //     final response = await _httpClient.patch(
  //       '/api/listings/$listingId/status/',
  //       data: {'status': status.name},
  //     );
  //     Logger.info('ListingApiService: Status updated successfully');
  //     return Listing.fromJson(response);
  //   } on DioException catch (e) {
  //     Logger.error('ListingApiService: Failed to update status', e);
  //     throw Exception('Failed to update status: ${e.message}');
  //   }
  // }
}