import '../services/listing_api_service.dart';
import '../models/listing_model.dart';
import '../../../../core/services/http_client.dart';


abstract class ListingRepository {
  //new methods for the listing creation flow
  Future<Listing> createInitialListing(Map<String, dynamic> initialData);
  Future<Listing> updateBasicInfo(String listingId, Map<String, dynamic> basicInfo);
  Future<Listing> updateAmenities(String listingId, List<String> amenities);
  Future<Listing> updatePricingPolicy(String listingId, Map<String, dynamic> pricingData);
  // Future<Listing> createListing(Map<String, dynamic> listingData);
  // Future<List<Listing>> getHostListings();
  // Future<Listing> getListingById(String listingId);
  // Future<Listing> updateListing(String listingId, Map<String, dynamic> updates);
  // Future<void> deleteListing(String listingId);
  // Future<Listing> submitForReview(String listingId);
  // Future<List<String>?> uploadPhotos(String listingId, List<String> photoPaths);
  // Future<List<String>?> uploadVideos(String listingId, List<String> videoPaths);
  // Future<void> autoSave(String listingId, Map<String, dynamic> updates);
  // Future<Map<String, dynamic>> getListingAnalytics(String listingId);
  // Future<Listing> updateListingStatus(String listingId, ListingStatus status);
}

class ListingRepositoryImpl implements ListingRepository {
  final ListingApiService _apiService;

  ListingRepositoryImpl({
    ListingApiService? apiService,
    HttpClient? httpClient,
  }) : _apiService = apiService ?? ListingApiService(
          httpClient: httpClient,
        );
    
  @override
  Future<Listing> createInitialListing(Map<String, dynamic> initialData) async {
    return await _apiService.createListing(initialData);
  }

  @override
  Future<Listing> updateBasicInfo(String listingId, Map<String, dynamic> basicInfo) async {
    return await _apiService.updateBasicInfo(listingId, basicInfo);
  }

  @override
  Future<Listing> updateAmenities(String listingId, List<String> amenities) async {
    return await _apiService.updateAmenities(listingId, amenities);
  }

  @override
  Future<Listing> updatePricingPolicy(String listingId, Map<String, dynamic> pricingData) async {
    return await _apiService.updatePricingPolicy(listingId, pricingData);
  }

//   @override
//   Future<Listing> createListing(Map<String, dynamic> listingData) async {
//     return await _apiService.createListing(listingData);
//   }

//   @override
//   Future<List<Listing>> getHostListings() async {
//     return await _apiService.getHostListings();
//   }

//   @override
//   Future<Listing> getListingById(String listingId) async {
//     return await _apiService.getListingById(listingId);
//   }

//   @override
//   Future<Listing> updateListing(String listingId, Map<String, dynamic> updates) async {
//     return await _apiService.updateListing(listingId, updates);
//   }

//   @override
//   Future<void> deleteListing(String listingId) async {
//     return await _apiService.deleteListing(listingId);
//   }

//   @override
//   Future<Listing> submitForReview(String listingId) async {
//     return await _apiService.submitForReview(listingId);
//   }

//   @override
//   Future<List<String>?> uploadPhotos(String listingId, List<String> photoPaths) async {
//     return await _apiService.uploadPhotos(listingId, photoPaths);
//   }

//   @override
//   Future<List<String>?> uploadVideos(String listingId, List<String> videoPaths) async {
//     return await _apiService.uploadVideos(listingId, videoPaths);
//   }

//   @override
//   Future<void> autoSave(String listingId, Map<String, dynamic> updates) async {
//     return await _apiService.autoSave(listingId, updates);
//   }

//   @override
//   Future<Map<String, dynamic>> getListingAnalytics(String listingId) async {
//     return await _apiService.getListingAnalytics(listingId);
//   }

//   @override
//   Future<Listing> updateListingStatus(String listingId, ListingStatus status) async {
//     return await _apiService.updateListingStatus(listingId, status);
//   }
}
