class Listing {
  final String listingId;
  final String status;
  final String? propertyType;
  final String? title;
  final String? description;
  final String? address;
  final double? latitude;
  final double? longitude;
  final int? rooms;
  final int? maxGuests;
  final String? checkIn;
  final String? checkOut;
  final List<String>? amenities;
  final List<String>? extras;
  final double? basePrice;
  final String? currency;
  final double? cleaningFee;
  final double? extraGuestFee;
  final String? cancellationPolicy;
  final int? minStayNights;
  final int? maxStayNights;
  final List<String>? photoUrls;
  final List<String>? availableDates;
  final List<String>? blackoutDates;
  final int? advanceNoticeDays;
  final String? rejectionReason;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Listing({
    required this.listingId,
    required this.status,
    this.propertyType,
    this.title,
    this.description,
    this.address,
    this.latitude,
    this.longitude,
    this.rooms,
    this.maxGuests,
    this.checkIn,
    this.checkOut,
    this.amenities,
    this.extras,
    this.basePrice,
    this.currency,
    this.cleaningFee,
    this.extraGuestFee,
    this.cancellationPolicy,
    this.minStayNights,
    this.maxStayNights,
    this.photoUrls,
    this.availableDates,
    this.blackoutDates,
    this.advanceNoticeDays,
    this.rejectionReason,
    this.createdAt,
    this.updatedAt,
  });

  // Factory constructor for creating from JSON
  factory Listing.fromJson(Map<String, dynamic> json) {
    return Listing(
      listingId: json['listing_id'] ?? '',
      status: json['status'] ?? 'draft',
      propertyType: json['property_type'],
      title: json['title'],
      description: json['description'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      rooms: json['rooms']?.toInt(),
      maxGuests: json['max_guests']?.toInt(),
      checkIn: json['check_in'],
      checkOut: json['check_out'],
      amenities: json['amenities'] != null 
          ? List<String>.from(json['amenities'])
          : null,
      extras: json['extras'] != null 
          ? List<String>.from(json['extras'])
          : null,
      basePrice: json['base_price']?.toDouble(),
      currency: json['currency'],
      cleaningFee: json['cleaning_fee']?.toDouble(),
      extraGuestFee: json['extra_guest_fee']?.toDouble(),
      cancellationPolicy: json['cancellation_policy'],
      minStayNights: json['min_stay_nights']?.toInt(),
      maxStayNights: json['max_stay_nights']?.toInt(),
      photoUrls: json['photo_urls'] != null 
          ? List<String>.from(json['photo_urls'])
          : null,
      availableDates: json['available_dates'] != null 
          ? List<String>.from(json['available_dates'])
          : null,
      blackoutDates: json['blackout_dates'] != null 
          ? List<String>.from(json['blackout_dates'])
          : null,
      advanceNoticeDays: json['advance_notice_days']?.toInt(),
      rejectionReason: json['reason'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  // Convert to JSON for API requests
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    
    if (propertyType != null) data['property_type'] = propertyType;
    if (title != null) data['title'] = title;
    if (description != null) data['description'] = description;
    if (address != null) data['address'] = address;
    if (latitude != null) data['latitude'] = latitude;
    if (longitude != null) data['longitude'] = longitude;
    if (rooms != null) data['rooms'] = rooms;
    if (maxGuests != null) data['max_guests'] = maxGuests;
    if (checkIn != null) data['check_in'] = checkIn;
    if (checkOut != null) data['check_out'] = checkOut;
    if (amenities != null) data['amenities'] = amenities;
    if (extras != null) data['extras'] = extras;
    if (basePrice != null) data['base_price'] = basePrice;
    if (currency != null) data['currency'] = currency;
    if (cleaningFee != null) data['cleaning_fee'] = cleaningFee;
    if (extraGuestFee != null) data['extra_guest_fee'] = extraGuestFee;
    if (cancellationPolicy != null) data['cancellation_policy'] = cancellationPolicy;
    if (minStayNights != null) data['min_stay_nights'] = minStayNights;
    if (maxStayNights != null) data['max_stay_nights'] = maxStayNights;
    if (availableDates != null) data['available_dates'] = availableDates;
    if (blackoutDates != null) data['blackout_dates'] = blackoutDates;
    if (advanceNoticeDays != null) data['advance_notice_days'] = advanceNoticeDays;

    return data;
  }

  // Method to get basic info JSON for PATCH /basic-info
  Map<String, dynamic> toBasicInfoJson() {
    return {
      if (propertyType != null) 'property_type': propertyType,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (address != null) 'address': address,
      if (latitude != null) 'latitude': latitude,
      if (longitude != null) 'longitude': longitude,
      if (rooms != null) 'rooms': rooms,
      if (maxGuests != null) 'max_guests': maxGuests,
      if (checkIn != null) 'check_in': checkIn,
      if (checkOut != null) 'check_out': checkOut,
    };
  }

  // Method to get amenities JSON for PATCH /amenities
  Map<String, dynamic> toAmenitiesJson() {
    return {
      if (amenities != null) 'amenities': amenities,
      if (extras != null) 'extras': extras,
    };
  }

  // Method to get pricing JSON for PATCH /pricing
  Map<String, dynamic> toPricingJson() {
    return {
      if (basePrice != null) 'base_price': basePrice,
      if (currency != null) 'currency': currency,
      if (cleaningFee != null) 'cleaning_fee': cleaningFee,
      if (extraGuestFee != null) 'extra_guest_fee': extraGuestFee,
      if (cancellationPolicy != null) 'cancellation_policy': cancellationPolicy,
      if (minStayNights != null) 'min_stay_nights': minStayNights,
      if (maxStayNights != null) 'max_stay_nights': maxStayNights,
    };
  }

  // Method to get availability JSON for PATCH /availability
  Map<String, dynamic> toAvailabilityJson() {
    return {
      if (availableDates != null) 'available_dates': availableDates,
      if (blackoutDates != null) 'blackout_dates': blackoutDates,
      if (advanceNoticeDays != null) 'advance_notice_days': advanceNoticeDays,
    };
  }

  // Method to get submit JSON for POST /submit
  Map<String, dynamic> toSubmitJson() {
    return {
      'confirm_publish': true,
    };
  }

  // Method to get admin status update JSON for PATCH /admin/listings/{id}/status
  // Map<String, dynamic> toAdminStatusJson({String? reason}) {
  //   final data = {
  //     'status': status,
  //   };
  //   if (reason != null) {
  //     data['reason'] = reason;
  //   }
  //   return data;
  // }

  // Copy with method for state management
  Listing copyWith({
    String? listingId,
    String? status,
    String? propertyType,
    String? title,
    String? description,
    String? address,
    double? latitude,
    double? longitude,
    int? rooms,
    int? maxGuests,
    String? checkIn,
    String? checkOut,
    List<String>? amenities,
    List<String>? extras,
    double? basePrice,
    String? currency,
    double? cleaningFee,
    double? extraGuestFee,
    String? cancellationPolicy,
    int? minStayNights,
    int? maxStayNights,
    List<String>? photoUrls,
    List<String>? availableDates,
    List<String>? blackoutDates,
    int? advanceNoticeDays,
    String? rejectionReason,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Listing(
      listingId: listingId ?? this.listingId,
      status: status ?? this.status,
      propertyType: propertyType ?? this.propertyType,
      title: title ?? this.title,
      description: description ?? this.description,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      rooms: rooms ?? this.rooms,
      maxGuests: maxGuests ?? this.maxGuests,
      checkIn: checkIn ?? this.checkIn,
      checkOut: checkOut ?? this.checkOut,
      amenities: amenities ?? this.amenities,
      extras: extras ?? this.extras,
      basePrice: basePrice ?? this.basePrice,
      currency: currency ?? this.currency,
      cleaningFee: cleaningFee ?? this.cleaningFee,
      extraGuestFee: extraGuestFee ?? this.extraGuestFee,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      minStayNights: minStayNights ?? this.minStayNights,
      maxStayNights: maxStayNights ?? this.maxStayNights,
      photoUrls: photoUrls ?? this.photoUrls,
      availableDates: availableDates ?? this.availableDates,
      blackoutDates: blackoutDates ?? this.blackoutDates,
      advanceNoticeDays: advanceNoticeDays ?? this.advanceNoticeDays,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Convenience getters for status checking
  bool get isDraft => status == 'draft';
  bool get isPendingReview => status == 'pending_review';
  bool get isActive => status == 'active';
  bool get isRejected => status == 'rejected';

  // Validation methods
  bool get hasBasicInfo => 
    propertyType != null && 
    title != null && 
    description != null && 
    address != null;

  bool get hasPricing => basePrice != null && currency != null;

  bool get hasPhotos => photoUrls != null && photoUrls!.isNotEmpty;

  bool get isReadyForSubmission => 
    hasBasicInfo && 
    hasPricing && 
    hasPhotos && 
    amenities != null;

  @override
  String toString() {
    return 'Listing(listingId: $listingId, status: $status, title: $title)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Listing && other.listingId == listingId;
  }

  @override
  int get hashCode => listingId.hashCode;
}

// Enum for listing status
enum ListingStatus {
  draft('draft'),
  pendingReview('pending_review'),
  active('active'),
  rejected('rejected');

  const ListingStatus(this.value);
  final String value;

  static ListingStatus fromString(String status) {
    return ListingStatus.values.firstWhere(
      (e) => e.value == status,
      orElse: () => ListingStatus.draft,
    );
  }
}

// Enum for property types
enum PropertyType {
  apartment('apartment'),
  house('house'),
  room('room'),
  studio('studio'),
  villa('villa');

  const PropertyType(this.value);
  final String value;

  static PropertyType fromString(String type) {
    return PropertyType.values.firstWhere(
      (e) => e.value == type,
      orElse: () => PropertyType.apartment,
    );
  }
}

// Enum for cancellation policies
enum CancellationPolicy {
  flexible('flexible'),
  moderate('moderate'),
  strict('strict');

  const CancellationPolicy(this.value);
  final String value;

  static CancellationPolicy fromString(String policy) {
    return CancellationPolicy.values.firstWhere(
      (e) => e.value == policy,
      orElse: () => CancellationPolicy.flexible,
    );
  }
}

// Common amenities constants
class Amenities {
  static const String wifi = 'wifi';
  static const String parking = 'parking';
  static const String pool = 'pool';
  static const String airConditioning = 'air_conditioning';
  static const String kitchen = 'kitchen';
  static const String tv = 'tv';
  static const String washer = 'washer';
  static const String gym = 'gym';
  static const String petFriendly = 'pet_friendly';
  static const String wheelchairAccess = 'wheelchair_access';

  static const List<String> all = [
    wifi,
    parking,
    pool,
    airConditioning,
    kitchen,
    tv,
    washer,
    gym,
    petFriendly,
    wheelchairAccess,
  ];
}