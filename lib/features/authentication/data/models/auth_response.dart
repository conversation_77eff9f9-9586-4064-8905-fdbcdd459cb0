import 'user_model.dart';

class AuthResponse {
  final bool success;
  final String? message;
  final AuthData? data;
  final String? error;
  final Map<String, dynamic>? errors; // For validation errors

  const AuthResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.errors,
  });

  //factory method
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    try {
      print('=== AuthResponse.fromJson START ===');
      print('Input JSON: $json');

      final success = json['success'] ?? false;
      final message = json['message'];
      final error = json['error'];
      final errors = json['errors'] as Map<String, dynamic>?;

      print('Parsed basic fields: success=$success, message=$message, error=$error');

      AuthData? data;
      if (json['data'] != null) {
        print('Data field exists, calling AuthData.fromJson()');
        data = AuthData.fromJson(json['data']);
        print('AuthData.fromJson() completed successfully');
      } else {
        print('Data field is null');
      }

      final result = AuthResponse(
        success: success,
        message: message,
        data: data,
        error: error,
        errors: errors,
      );

      print('AuthResponse created successfully: $result');
      print('=== AuthResponse.fromJson END ===');
      return result;
    } catch (e, stackTrace) {
      print('AuthResponse.fromJson ERROR: $e');
      print('Stack trace: $stackTrace');
      print('Input JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'error': error,
      'errors': errors,
    };
  }

  @override
  String toString() {
    return 'AuthResponse(success: $success, message: $message, data: $data, error: $error, errors: $errors)';
  }
}

class AuthData {
  final String? accessToken;
  final String? refreshToken;
  final UserModel? user;
  final int? expiresIn;
  final String? tokenType;

  const AuthData({
    this.accessToken,
    this.refreshToken,
    this.user,
    this.expiresIn,
    this.tokenType = 'Bearer',
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    try {
      print('AuthData.fromJson: $json');
      final userData = json['user'];
      print('User data: $userData');
      print('User data type: ${userData.runtimeType}');

      return AuthData(
        accessToken: json['access_token'],
        refreshToken: json['refresh_token'],
        user: userData != null ? UserModel.fromJson(userData) : null,
        expiresIn: json['expires_in'],
        tokenType: json['token_type'] ?? 'Bearer',
      );
    } catch (e) {
      print('AuthData.fromJson error: $e');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'user': user?.toJson(),
      'expires_in': expiresIn,
      'token_type': tokenType,
    };
  }

  @override
  String toString() {
    return 'AuthData(accessToken: $accessToken, refreshToken: $refreshToken, user: $user, expiresIn: $expiresIn, tokenType: $tokenType)';
  }
}
