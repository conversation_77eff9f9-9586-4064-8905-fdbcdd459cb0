import '../../../../core/services/http_client.dart';
import '../../../../core/utils/logger.dart';
import '../models/auth_response.dart';
import '../models/login_request_model.dart';
import '../models/register_request_model.dart';
import '../../../../core/constants/api_constants.dart';

class AuthApiService {
  //static const String _baseEndpoint = '';
  
  final HttpClient _httpClient = HttpClient.instance;

  // Register user--testing one 
  Future<AuthResponse> register(RegisterRequestModel request) async {
    try {
      print('=== AUTH API SERVICE REGISTER START ===');
      print('AuthApiService: Registering user');
      print('Endpoint: ${ApiConstants.authRegister}');
      print('Full URL: ${ApiConstants.baseUrlV1}${ApiConstants.authRegister}');
      print('Request data: ${request.toJson()}');
      
      //response is a map
      final response = await _httpClient.post(
        ApiConstants.authRegister,
        data: request.toJson(),
      );

      print('AuthApiService: Registration response received');
      print('Raw response: $response');
      print('Response type: ${response.runtimeType}');
      
      final authResponse = AuthResponse.fromJson(response);
      print('Parsed AuthResponse: success=${authResponse.success}, message=${authResponse.message}');
      Logger.info('Parsed AuthResponse: success=${authResponse.success}, message=${authResponse.message}');
      Logger.info('=== AUTH API SERVICE REGISTER COMPLETE ===');

      return authResponse;
    } catch (e, stackTrace) {
      print('AuthApiService: Registration failed - $e');
      print('Error type: ${e.runtimeType}');
      print('Stack trace: $stackTrace');
      Logger.error('AuthApiService: Registration failed - $e');
      Logger.error('Error type: ${e.runtimeType}');
      Logger.error('Stack trace: $stackTrace');
      Logger.error('=== AUTH API SERVICE REGISTER ERROR ===');
      rethrow;
    }
  }

  // Verify OTP
  Future<AuthResponse> verifyOtp(VerifyOtpRequestModel request) async {
    try {
      Logger.info('AuthApiService: Verifying OTP');
      
      final response = await _httpClient.post(
        ApiConstants.authVerifyEmail,
        data: request.toJson(),
      );
      
      Logger.info('AuthApiService: OTP verification response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: OTP verification failed - $e');
      rethrow;
    }
  }

  // Resend OTP---updating using email instead of phone number 
  Future<AuthResponse> resendOtp(String email) async {
    try {
      Logger.info('AuthApiService: Resending OTP');
      
      final response = await _httpClient.post(
        ApiConstants.authResendOtp,
        data: {'email': email},
      );
      
      Logger.info('AuthApiService: Resend OTP response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Resend OTP failed - $e');
      rethrow;
    }
  }

  // Login user
  Future<AuthResponse> login(LoginRequestModel request) async {
    try {
      print('🚀 AUTH API SERVICE: LOGIN START');
      Logger.info('AuthApiService: Logging in user');

      final response = await _httpClient.post(
        ApiConstants.authLogin,
        data: request.toJson(),
      );

      print('✅ AUTH API SERVICE: HTTP response received');
      Logger.info('AuthApiService: Login response received');

      final authResponse = AuthResponse.fromJson(response);
      print('✅ AUTH API SERVICE: AuthResponse parsed successfully');
      Logger.info('AuthResponse parsed successfully');

      return authResponse;
    } catch (e, stackTrace) {
      print('❌ AUTH API SERVICE: Login failed - $e');
      print('Stack trace: $stackTrace');
      Logger.error('AuthApiService: Login failed - $e');
      rethrow;
    }
  }

  // Forgot password
  Future<AuthResponse> forgotPassword(ForgotPasswordRequestModel request) async {
    try {
      Logger.info('AuthApiService: Forgot password request');
      
      final response = await _httpClient.post(
        ApiConstants.authForgotPassword,
        data: request.toJson(),
      );
      
      Logger.info('AuthApiService: Forgot password response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Forgot password failed - $e');
      rethrow;
    }
  }

  // Reset password
  Future<AuthResponse> resetPassword(ResetPasswordRequestModel request) async {
    try {
      Logger.info('AuthApiService: Reset password request');
      
      final response = await _httpClient.post(
        ApiConstants.authResetPassword,
        data: request.toJson(),
      );
      
      Logger.info('AuthApiService: Reset password response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Reset password failed - $e');
      rethrow;
    }
  }

  // Logout user
  Future<AuthResponse> logout() async {
    try {
      Logger.info('AuthApiService: Logging out user');
      
      final response = await _httpClient.post(
        ApiConstants.authLogout,
      );
      
      Logger.info('AuthApiService: Logout response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Logout failed - $e');
      rethrow;
    }
  }

  // Refresh token
  Future<AuthResponse> refreshToken(String refreshToken) async {
    try {
      Logger.info('AuthApiService: Refreshing token');
      
      final response = await _httpClient.post(
        ApiConstants.authRefresh,
        data: {'refresh_token': refreshToken},
      );
      
      Logger.info('AuthApiService: Token refresh response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Token refresh failed - $e');
      rethrow;
    }
  }

  // Get user profile
  Future<AuthResponse> getUserProfile() async {
    try {
      Logger.info('AuthApiService: Getting user profile');
      
      final response = await _httpClient.get(
        ApiConstants.authProfile,
      );
      
      Logger.info('AuthApiService: User profile response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Get user profile failed - $e');
      rethrow;
    }
  }

  // Update user profile
  Future<AuthResponse> updateUserProfile(Map<String, dynamic> userData) async {
    try {
      Logger.info('AuthApiService: Updating user profile');

      final response = await _httpClient.put(
        ApiConstants.userUpdate,
        data: userData,
      );

      Logger.info('AuthApiService: Update profile response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Update profile failed - $e');
      rethrow;
    }
  }

  // Change password
  Future<AuthResponse> changePassword(ChangePasswordRequestModel request) async {
    try {
      Logger.info('AuthApiService: Changing password');

      final response = await _httpClient.post(
        ApiConstants.authChangePassword,
        data: request.toJson(),
      );

      Logger.info('AuthApiService: Change password response received');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Change password failed - $e');
      rethrow;
    }
  }

  // Complete profile (for landlords after OTP verification)
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request) async {
    try {
      Logger.info('AuthApiService: Completing profile');
      Logger.info('Endpoint: ${ApiConstants.authCompleteProfile}');
      Logger.info('Full URL: ${ApiConstants.baseUrlV1}${ApiConstants.authCompleteProfile}');
      Logger.info('Request data: ${request.toJson()}');

      final response = await _httpClient.post(
        ApiConstants.authCompleteProfile,
        data: request.toJson(),
      );

      Logger.info('AuthApiService: Complete profile response received');
      Logger.info('Response: $response');
      return AuthResponse.fromJson(response);
    } catch (e) {
      Logger.error('AuthApiService: Complete profile failed - $e');
      rethrow;
    }
  }
}
