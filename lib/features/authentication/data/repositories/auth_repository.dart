import '../../../../core/services/storage_service.dart';
import '../../../../core/utils/logger.dart';
import '../models/auth_response.dart';
import '../models/login_request_model.dart';
import '../models/register_request_model.dart';
import '../models/user_model.dart';
import '../services/auth_api_service.dart';
import '../services/dummy_auth_api_service.dart';

abstract class AuthRepository {
  Future<AuthResponse> register(RegisterRequestModel request);
  Future<AuthResponse> verifyOtp(VerifyOtpRequestModel request);
  Future<AuthResponse> resendOtp(String phoneNumber);
  Future<AuthResponse> login(LoginRequestModel request);
  Future<AuthResponse> forgotPassword(ForgotPasswordRequestModel request);
  Future<AuthResponse> resetPassword(ResetPasswordRequestModel request);
  Future<AuthResponse> changePassword(ChangePasswordRequestModel request);
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request);
  Future<AuthResponse> logout();
  Future<AuthResponse> refreshToken(String refreshToken);
  Future<AuthResponse> getUserProfile();
  Future<AuthResponse> updateUserProfile(Map<String, dynamic> userData);
  Future<void> saveAuthData(AuthData authData);
  Future<void> clearAuthData();
  Future<UserModel?> getCurrentUser();
  Future<String?> getAuthToken();
  Future<String?> getRefreshToken();
  Future<bool> isLoggedIn();
}

class AuthRepositoryImpl implements AuthRepository {
  //handle the api call
  //final DummyAuthApiService _dummyApiService; // Using dummy service for testing
  final AuthApiService _apiService; // Real API service 
  //hanadle the local storage
  final StorageService _storageService;

  AuthRepositoryImpl({
    DummyAuthApiService? dummyApiService,
    AuthApiService? apiService,
    StorageService? storageService,
  })  : //_dummyApiService = dummyApiService ?? DummyAuthApiService(),
        _apiService = apiService ?? AuthApiService(),
        _storageService = storageService ?? StorageService.instance;

  @override
  Future<AuthResponse> register(RegisterRequestModel request) async {
    try {
      print('=== AUTH REPOSITORY REGISTER START ===');
      print('AuthRepository: Register request received');
      print('Request details: ${request.toString()}');
      print('Request JSON: ${request.toJson()}');
      
      print('AuthRepository: Calling API service register method');
      Logger.info('AuthRepository: Calling API service register method');
      final response = await _apiService.register(request);

      print('AuthRepository: API service response received');
      print('Response success: ${response.success}');
      print('Response message: ${response.message}');
      print('Response error: ${response.error}');
      print('Response data type: ${response.data.runtimeType}');
      print('Response data: ${response.data}');
      Logger.info('AuthRepository: API service response received');
      Logger.info('Response success: ${response.success}');
      Logger.info('Response message: ${response.message}');
      Logger.info('Response error: ${response.error}');
      Logger.info('Response data type: ${response.data.runtimeType}');
      Logger.info('Response data: ${response.data}');
      Logger.info('=== AUTH REPOSITORY REGISTER COMPLETE ===');

      return response;
    } catch (e, stackTrace) {
      print('AuthRepository: Register failed - $e');
      print('Stack trace: $stackTrace');
      Logger.error('AuthRepository: Register failed - $e');
      Logger.error('Stack trace: $stackTrace');
      Logger.error('=== AUTH REPOSITORY REGISTER ERROR ===');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> verifyOtp(VerifyOtpRequestModel request) async {
    try {
      Logger.info('AuthRepository: Verify OTP request');
      // TODO: Replace with _apiService.verifyOtp(request) when real API is ready
      final response = await _apiService.verifyOtp(request);

      // If verification successful and contains auth data, save it
      if (response.success && response.data != null) {
        await saveAuthData(response.data!);
        Logger.info('AuthRepository: Auth data saved after OTP verification');
      }

      Logger.info('AuthRepository: Verify OTP completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Verify OTP failed - $e');
      rethrow;
    }
  }

  //using the email to resend the otp instead of the phone 
  @override
  Future<AuthResponse> resendOtp(String phoneNumber) async {
    try {
      Logger.info('AuthRepository: Resend OTP request');
      // TODO: Replace with _apiService.resendOtp(phoneNumber) when real API is ready
      final response = await _apiService.resendOtp(phoneNumber);
      Logger.info('AuthRepository: Resend OTP completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Resend OTP failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> login(LoginRequestModel request) async {
    try {
      Logger.info('AuthRepository: Login request');
      // TODO: Replace with _apiService.login(request) when real API is ready
      final response = await _apiService.login(request);

      // If login successful, save auth data
      if (response.success && response.data != null) {
        await saveAuthData(response.data!);
        Logger.info('AuthRepository: Auth data saved after login');
      }

      Logger.info('AuthRepository: Login completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Login failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> forgotPassword(ForgotPasswordRequestModel request) async {
    try {
      Logger.info('AuthRepository: Forgot password request');
      // TODO: Replace with _apiService.forgotPassword(request) when real API is ready
      final response = await _apiService.forgotPassword(request);
      Logger.info('AuthRepository: Forgot password completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Forgot password failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> resetPassword(ResetPasswordRequestModel request) async {
    try {
      Logger.info('AuthRepository: Reset password request');
      // TODO: Replace with _apiService.resetPassword(request) when real API is ready
      final response = await _apiService.resetPassword(request);
      Logger.info('AuthRepository: Reset password completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Reset password failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> changePassword(ChangePasswordRequestModel request) async {
    try {
      Logger.info('AuthRepository: Change password request');
      final response = await _apiService.changePassword(request);
      Logger.info('AuthRepository: Change password completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Change password failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> completeProfile(CompleteProfileRequestModel request) async {
    try {
      Logger.info('AuthRepository: Complete profile request');
      final response = await _apiService.completeProfile(request);

      // If profile completion successful and contains auth data, save it
      if (response.success && response.data != null) {
        await saveAuthData(response.data!);
        Logger.info('AuthRepository: Auth data updated after profile completion');
      }

      Logger.info('AuthRepository: Complete profile completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Complete profile failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> logout() async {
    try {
      Logger.info('AuthRepository: Logout request');
      // TODO: Replace with _apiService.logout() when real API is ready
      final response = await _apiService.logout();

      // Clear auth data regardless of API response
      await clearAuthData();
      Logger.info('AuthRepository: Auth data cleared after logout');

      Logger.info('AuthRepository: Logout completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Logout failed - $e');
      // Still clear auth data on error
      await clearAuthData();
      rethrow;
    }
  }

  @override
  Future<AuthResponse> refreshToken(String refreshToken) async {
    try {
      Logger.info('AuthRepository: Refresh token request');
      final response = await _apiService.refreshToken(refreshToken);
      
      // If refresh successful, update auth data
      if (response.success && response.data != null) {
        await saveAuthData(response.data!);
        Logger.info('AuthRepository: Auth data updated after token refresh');
      }
      
      Logger.info('AuthRepository: Refresh token completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Refresh token failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> getUserProfile() async {
    try {
      Logger.info('AuthRepository: Get user profile request');
      final response = await _apiService.getUserProfile();
      
      // If successful and contains user data, update stored user
      if (response.success && response.data?.user != null) {
        await _storageService.setUserData(response.data!.user!.toJson());
        Logger.info('AuthRepository: User data updated');
      }
      
      Logger.info('AuthRepository: Get user profile completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Get user profile failed - $e');
      rethrow;
    }
  }

  @override
  Future<AuthResponse> updateUserProfile(Map<String, dynamic> userData) async {
    try {
      Logger.info('AuthRepository: Update user profile request');
      final response = await _apiService.updateUserProfile(userData);
      
      // If successful and contains user data, update stored user
      if (response.success && response.data?.user != null) {
        await _storageService.setUserData(response.data!.user!.toJson());
        Logger.info('AuthRepository: User data updated after profile update');
      }
      
      Logger.info('AuthRepository: Update user profile completed');
      return response;
    } catch (e) {
      Logger.error('AuthRepository: Update user profile failed - $e');
      rethrow;
    }
  }

  @override
  Future<void> saveAuthData(AuthData authData) async {
    try {
      Logger.info('AuthRepository: Saving auth data');
      
      if (authData.accessToken != null) {
        await _storageService.setAuthToken(authData.accessToken!);
      }
      
      if (authData.refreshToken != null) {
        await _storageService.setRefreshToken(authData.refreshToken!);
      }
      
      if (authData.user != null) {
        await _storageService.setUserData(authData.user!.toJson());
      }
      
      Logger.info('AuthRepository: Auth data saved successfully');
    } catch (e) {
      Logger.error('AuthRepository: Save auth data failed - $e');
      rethrow;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      Logger.info('AuthRepository: Clearing auth data');
      await _storageService.clearAuthData();
      Logger.info('AuthRepository: Auth data cleared successfully');
    } catch (e) {
      Logger.error('AuthRepository: Clear auth data failed - $e');
      rethrow;
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final userData = _storageService.getUserData();
      if (userData != null) {
        return UserModel.fromJson(userData);
      }
      return null;
    } catch (e) {
      Logger.error('AuthRepository: Get current user failed - $e');
      return null;
    }
  }

  @override
  Future<String?> getAuthToken() async {
    try {
      return _storageService.getAuthToken();
    } catch (e) {
      Logger.error('AuthRepository: Get auth token failed - $e');
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return _storageService.getRefreshToken();
    } catch (e) {
      Logger.error('AuthRepository: Get refresh token failed - $e');
      return null;
    }
  }

  //check if the user is logged in
  @override
  Future<bool> isLoggedIn() async {
    try {
      final token = await getAuthToken();
      final user = await getCurrentUser();
      return token != null && user != null;
    } catch (e) {
      Logger.error('AuthRepository: Check login status failed - $e');
      return false;
    }
  }
}
