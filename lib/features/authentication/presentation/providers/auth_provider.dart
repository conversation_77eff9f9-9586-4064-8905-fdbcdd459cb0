import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../../core/utils/logger.dart';
import '../../data/models/login_request_model.dart';
import '../../data/models/register_request_model.dart';
import '../../data/models/user_model.dart';
import '../../data/repositories/auth_repository.dart';
import '../../../../core/services/session_service.dart' as session;

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  otpRequired,
  error,
}

class AuthProvider extends ChangeNotifier {
  //auth request bridge
  final AuthRepository _authRepository;
  final session.SessionService _sessionService = session.SessionService.instance;

  AuthState _state = AuthState.initial;
  //user data
  UserModel? _currentUser;
  String? _errorMessage;
  String? _successMessage;
  String? _tempEmail; // For OTP flow-change to email

  AuthProvider({AuthRepository? authRepository})
      : _authRepository = authRepository ?? AuthRepositoryImpl() {
    _initializeSession();
  }

  // Getters
  AuthState get state => _state;
  UserModel? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  String? get tempPhoneNumber => _tempEmail; // For backward compatibility, returns email
  String? get successMessage => _successMessage;
  String? get tempEmail => _tempEmail;
  bool get isLoading => _state == AuthState.loading;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isOtpRequired => _state == AuthState.otpRequired;

  // Initialize session from stored data
  Future<void> _initializeSession() async {
    try {
      print('🔄 AUTH PROVIDER: Initializing session...');

      // Check if user is already logged in via SessionService
      if (_sessionService.isLoggedIn && _sessionService.currentUser != null) {
        print('✅ AUTH PROVIDER: Found existing session');

        // Convert SessionService User to UserModel
        final sessionUser = _sessionService.currentUser!;
        _currentUser = UserModel(
          id: sessionUser.id,
          firstName: sessionUser.name.split(' ').first,
          lastName: sessionUser.name.split(' ').length > 1 ? sessionUser.name.split(' ').last : '',
          email: sessionUser.email,
          phoneNumber: sessionUser.phoneNumber ?? '',
          role: _convertSessionRoleToUserRole(sessionUser.role),
          isVerified: sessionUser.isVerified,
          profilePicture: sessionUser.profileImageUrl,
        );

        _setState(AuthState.authenticated);
        print('✅ AUTH PROVIDER: Session restored for user: ${_currentUser?.email}');
      } else {
        print('ℹ️ AUTH PROVIDER: No existing session found');
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      print('❌ AUTH PROVIDER: Session initialization failed - $e');
      _setState(AuthState.unauthenticated);
    }
  }

  // Convert SessionService UserRole to UserModel UserRole
  UserRole _convertSessionRoleToUserRole(session.UserRole sessionRole) {
    switch (sessionRole) {
      case session.UserRole.customer:
        return UserRole.customer;
      case session.UserRole.household:
        return UserRole.landlord;
      case session.UserRole.admin:
        return UserRole.landlord; // Map admin to landlord since UserModel doesn't have admin
    }
  }

  // Convert UserModel UserRole to SessionService UserRole
  session.UserRole _convertUserRoleToSessionRole(UserRole userRole) {
    switch (userRole) {
      case UserRole.customer:
        return session.UserRole.customer;
      case UserRole.landlord:
        return session.UserRole.household;
    }
  }

  // Initialize auth state (backward compatibility)
  //happen in the app starts
  Future<void> initializeAuth() async {
    await _initializeSession();
  }

  // Register user
  Future<bool> register(RegisterRequestModel request) async {
    try {
      print('=== AUTH PROVIDER: REGISTER START ===');
      print('Request: ${request.toString()}');
      print('Request JSON: ${request.toJson()}');
      Logger.info('=== REGISTRATION FLOW START ===');
      Logger.info('AuthProvider: Starting registration process');
      Logger.info('Request data: ${request.toString()}');
      Logger.info('Request JSON: ${request.toJson()}');

      _setState(AuthState.loading);
      _clearMessages();

      print('AuthProvider: Calling auth repository register method');
      Logger.info('AuthProvider: Calling auth repository register method');
      final response = await _authRepository.register(request);

      print('AuthProvider: Received response from repository');
      print('Response success: ${response.success}');
      print('Response message: ${response.message}');
      Logger.info('AuthProvider: Received response from repository');
      Logger.info('Response success: ${response.success}');
      Logger.info('Response message: ${response.message}');
      Logger.info('Response error: ${response.error}');
      Logger.info('Response data: ${response.data}');

      if (response.success) {
        // Store email for OTP flow
        _tempEmail = request.email;
        _successMessage = response.message ?? 'Registration successful! Please verify your OTP.';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: Registration successful, OTP required');
        Logger.info('Stored temp email: $_tempEmail');
        Logger.info('=== REGISTRATION FLOW SUCCESS ===');
        return true;
      } else {
        // Handle validation errors
        if (response.errors != null && response.errors!.isNotEmpty) {
          // Extract specific validation errors
          final errorMessages = <String>[];
          response.errors!.forEach((field, messages) {
            if (messages is List) {
              errorMessages.addAll(messages.map((msg) => msg.toString()));
            } else {
              errorMessages.add(messages.toString());
            }
          });
          _errorMessage = errorMessages.join('\n');
        } else {
          _errorMessage = response.error ?? response.message ?? 'Registration failed';
        }
        _setState(AuthState.error);
        Logger.error('AuthProvider: Registration failed - ${_errorMessage}');
        Logger.error('Validation errors: ${response.errors}');
        Logger.error('=== REGISTRATION FLOW FAILED ===');
        return false;
      }
    } catch (e, stackTrace) {
      Logger.error('AuthProvider: Registration error - $e');
      Logger.error('Stack trace: $stackTrace');
      _errorMessage = 'Registration failed. Please try again.';
      _setState(AuthState.error);
      Logger.error('=== REGISTRATION FLOW ERROR ===');
      return false;
    }
  }

  // Verify OTP
  Future<bool> verifyOtp(String otp) async {
    try {
      if (_tempEmail == null) {
        _errorMessage = 'email number not found. Please register again.';
        _setState(AuthState.error);
        return false;
      }

      _setState(AuthState.loading);
      _clearMessages();
      
      //model ---hanadle the data and everthing in it very well 
      final request = VerifyOtpRequestModel(
        email: _tempEmail!,
        otp: otp,
      );
      
      final response = await _authRepository.verifyOtp(request);
      //otp verification should return the user from it 
      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        _successMessage = response.message ?? 'Account verified successfully!';
        _setState(AuthState.authenticated);
        _tempEmail = null; // Clear temp phone number
        Logger.info('AuthProvider: OTP verification successful');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'OTP verification failed';
        _setState(AuthState.error);
        Logger.error('AuthProvider: OTP verification failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: OTP verification error - $e');
      _errorMessage = 'OTP verification failed. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Resend OTP
  Future<bool> resendOtp() async {
    try {
      if (_tempEmail == null) {
        _errorMessage = 'Phone number not found. Please register again.';
        _setState(AuthState.error);
        return false;
      }

      _setState(AuthState.loading);
      _clearMessages();
      
      final response = await _authRepository.resendOtp(_tempEmail!);
      
      if (response.success) {
        _successMessage = response.message ?? 'OTP sent successfully!';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: OTP resent successfully');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Failed to resend OTP';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Resend OTP failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: Resend OTP error - $e');
      _errorMessage = 'Failed to resend OTP. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Login user
  Future<bool> login(LoginRequestModel request) async {
    try {
      print('=== AUTH PROVIDER: LOGIN START ===');
      print('Request: $request');
      Logger.info('=== AUTH PROVIDER: LOGIN START ===');
      Logger.info('Request: $request');
      _setState(AuthState.loading);
      _clearMessages();

      final response = await _authRepository.login(request);

      Logger.info('AuthProvider: Received response from repository');
      Logger.info('Response success: ${response.success}');
      Logger.info('Response message: ${response.message}');
      Logger.info('Response error: ${response.error}');
      Logger.info('Response data type: ${response.data.runtimeType}');
      Logger.info('Response data: ${response.data}');
      Logger.info('Response data user: ${response.data?.user}');
      Logger.info('Response data user type: ${response.data?.user.runtimeType}');

      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        _successMessage = response.message ?? 'Login successful!';

        // Save session to SessionService for persistence
        try {
          final sessionUser = session.User(
            id: _currentUser!.id,
            email: _currentUser!.email,
            name: '${_currentUser!.firstName} ${_currentUser!.lastName}',
            role: _convertUserRoleToSessionRole(_currentUser!.role),
            phoneNumber: _currentUser!.phoneNumber,
            profileImageUrl: _currentUser!.profilePicture,
            isEmailVerified: _currentUser!.isVerified,
            isPhoneVerified: _currentUser!.isVerified,
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          );

          await _sessionService.login(
            user: sessionUser,
            authToken: response.data?.accessToken ?? 'mock_token',
            refreshToken: response.data?.refreshToken ?? 'mock_refresh_token',
          );

          print('✅ AUTH PROVIDER: Session saved successfully');
        } catch (sessionError) {
          print('⚠️ AUTH PROVIDER: Failed to save session - $sessionError');
          // Continue with login even if session save fails
        }

        _setState(AuthState.authenticated);
        Logger.info('AuthProvider: Login successful');
        Logger.info('Current user set: $_currentUser');
        Logger.info('Auth state: $_state');
        Logger.info('=== AUTH PROVIDER: LOGIN SUCCESS ===');
        return true;
      } else {
        Logger.error('AuthProvider: Login failed - condition check failed');
        Logger.error('response.success: ${response.success}');
        Logger.error('response.data: ${response.data}');
        Logger.error('response.data?.user: ${response.data?.user}');
        Logger.error('response.data?.user != null: ${response.data?.user != null}');

        _errorMessage = response.error ?? response.message ?? 'Login failed';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Login failed - $_errorMessage');
        Logger.error('=== AUTH PROVIDER: LOGIN FAILED ===');
        return false;
      }
    } catch (e, stackTrace) {
      Logger.error('AuthProvider: Login error - $e');
      Logger.error('Stack trace: $stackTrace');
      _errorMessage = 'Login failed. Please check your credentials.';
      _setState(AuthState.error);
      Logger.error('=== AUTH PROVIDER: LOGIN ERROR ===');
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(ForgotPasswordRequestModel request) async {
    try {
      _setState(AuthState.loading);
      _clearMessages();
      
      final response = await _authRepository.forgotPassword(request);
      
      if (response.success) {
        _tempEmail = request.email;
        _successMessage = response.message ?? 'Password reset OTP sent successfully!';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: Forgot password OTP sent');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Failed to send reset OTP';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Forgot password failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: Forgot password error - $e');
      _errorMessage = 'Failed to send reset OTP. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Reset password
  Future<bool> resetPassword(String phone, String newPassword, String otp) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final request = ResetPasswordRequestModel(
        email: phone, // This should be email, but keeping parameter name for now
        otp: otp,
        newPassword: newPassword,
        confirmPassword: newPassword,
      );

      final response = await _authRepository.resetPassword(request);

      if (response.success) {
        _successMessage = response.message ?? 'Password reset successfully!';
        _setState(AuthState.unauthenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to reset password';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> userData) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final response = await _authRepository.updateUserProfile(userData);

      if (response.success) {
        // Update current user data if available
        if (response.data?.user != null) {
          _currentUser = response.data!.user;
        }
        _successMessage = response.message ?? 'Profile updated successfully!';
        _setState(AuthState.authenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to update profile';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Change password
  Future<bool> changePassword(ChangePasswordRequestModel request) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final response = await _authRepository.changePassword(request);

      if (response.success) {
        _successMessage = response.message ?? 'Password changed successfully!';
        _setState(AuthState.authenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to change password';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Complete profile (for landlords after OTP verification)
  Future<bool> completeProfile(CompleteProfileRequestModel request) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final response = await _authRepository.completeProfile(request);

      if (response.success) {
        // Update current user data if available in response
        if (response.data?.user != null) {
          _currentUser = response.data!.user;
          // Update session with new user data
          await _sessionService.updateUser(_convertUserModelToUser(_currentUser!));
        }

        _successMessage = response.message ?? 'Profile completed successfully! Waiting for admin approval.';
        _setState(AuthState.authenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to complete profile';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      Logger.error('AuthProvider: Complete profile failed - $e');
      return false;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      _setState(AuthState.loading);

      await _authRepository.logout();

      // Clear session from SessionService
      await _sessionService.clearSession();

      _currentUser = null;
      _tempEmail = null;
      _clearMessages();
      _setState(AuthState.unauthenticated);
      print('✅ AUTH PROVIDER: Logout successful - session cleared');
      Logger.info('AuthProvider: Logout successful');
    } catch (e) {
      Logger.error('AuthProvider: Logout error - $e');
      // Still clear local state even if API call fails
      try {
        await _sessionService.clearSession();
      } catch (sessionError) {
        print('⚠️ AUTH PROVIDER: Failed to clear session - $sessionError');
      }

      _currentUser = null;
      _tempEmail = null;
      _clearMessages();
      _setState(AuthState.unauthenticated);
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    try {
      if (_state != AuthState.authenticated) return;
      
      final response = await _authRepository.getUserProfile();
      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        notifyListeners();
        Logger.info('AuthProvider: User data refreshed');
      }
    } catch (e) {
      Logger.error('AuthProvider: Refresh user data error - $e');
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _setState(AuthState.unauthenticated);
    }
  }

  // Clear success message
  void clearSuccess() {
    _successMessage = null;
    notifyListeners();
  }

  // Helper methods
  //it set the state and notify the listeners to rebuild ...
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  // Convert UserModel to session User
  session.User _convertUserModelToUser(UserModel userModel) {
    return session.User(
      id: userModel.id,
      email: userModel.email,
      name: '${userModel.firstName} ${userModel.lastName}',
      role: _convertUserRole(userModel.role),
      phoneNumber: userModel.phoneNumber,
      profileImageUrl: userModel.profilePicture,
      isEmailVerified: userModel.isVerified,
      isPhoneVerified: userModel.isVerified,
      createdAt: userModel.createdAt ?? DateTime.now(),
      lastLoginAt: DateTime.now(),
    );
  }

  // Convert UserRole to session UserRole
  session.UserRole _convertUserRole(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return session.UserRole.customer;
      case UserRole.landlord:
        return session.UserRole.household; // Map landlord to household in session
    }
  }

  // Get home route based on user role
  String getHomeRoute() {
    if (_currentUser == null) return '/login';

    // Route based on user role
    if (_currentUser!.role == UserRole.landlord) {
      // Check if profile is complete - for now, always go to complete profile first
      // In a real app, you'd check if the user has already completed their profile
      return '/host-complete-profile';
    } else {
      return '/main-navigation'; // Customers go to main navigation
    }
  }
}
