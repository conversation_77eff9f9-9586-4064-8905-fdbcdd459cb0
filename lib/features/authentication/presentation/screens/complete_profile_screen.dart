import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';
import '../providers/auth_provider.dart';
import '../../data/models/register_request_model.dart';

class CompleteProfileScreen extends StatefulWidget {
  const CompleteProfileScreen({super.key});

  @override
  State<CompleteProfileScreen> createState() => _CompleteProfileScreenState();
}

class _CompleteProfileScreenState extends State<CompleteProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dateOfBirthController = TextEditingController();
  final _addressController = TextEditingController();
  final _nidaController = TextEditingController();

  @override
  void dispose() {
    _dateOfBirthController.dispose();
    _addressController.dispose();
    _nidaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        automaticallyImplyLeading: false,
        title: Text(
          'Complete Your Profile',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  _buildHeaderSection(),
                  const SizedBox(height: 32),

                  // Form Fields
                  _buildFormFields(),
                  const SizedBox(height: 32),

                  // Submit Button
                  _buildSubmitButton(authProvider),
                  const SizedBox(height: 16),

                  // Skip Button (Optional)
                  _buildSkipButton(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.primaryGradient,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.person_add,
            size: 48,
            color: AppColors.textOnPrimary,
          ),
          const SizedBox(height: 16),
          Text(
            'Welcome to EMakazi Host!',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please complete your profile to start listing properties and managing bookings.',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 20),

        // Date of Birth
        AuthTextField(
          controller: _dateOfBirthController,
          label: 'Date of Birth',
          hint: 'YYYY-MM-DD (e.g., 1992-07-20)',
          prefixIcon: const Icon(Icons.calendar_today),
          keyboardType: TextInputType.text,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9\-/]')),
            LengthLimitingTextInputFormatter(10),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your date of birth';
            }

            // Convert slashes to dashes for consistency
            String normalizedValue = value.replaceAll('/', '-');

            // Validate YYYY-MM-DD format
            final dateRegex = RegExp(r'^\d{4}-\d{2}-\d{2}$');
            if (!dateRegex.hasMatch(normalizedValue)) {
              return 'Please use YYYY-MM-DD or YYYY/MM/DD format';
            }

            // Try to parse the date
            try {
              final date = DateTime.parse(normalizedValue);
              final now = DateTime.now();
              final age = now.year - date.year;

              if (age < 18 || age > 100) {
                return 'Age must be between 18 and 100 years';
              }
            } catch (e) {
              return 'Please enter a valid date';
            }

            return null;
          },
        ),
        const SizedBox(height: 16),

        // Address
        AuthTextField(
          controller: _addressController,
          label: 'Address',
          hint: 'e.g., 123 Kariakoo Street, Dar es Salaam',
          prefixIcon: const Icon(Icons.location_on),
          maxLines: 2,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your address';
            }
            if (value.length < 10) {
              return 'Please enter a complete address';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // NIDA Number
        AuthTextField(
          controller: _nidaController,
          label: 'NIDA Number',
          hint: '13 digits',
          prefixIcon: const Icon(Icons.credit_card),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(13),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your NIDA number';
            }
            if (value.length != 13) {
              return 'NIDA number must be exactly 13 digits';
            }
            return null;
          },
        ),
        const SizedBox(height: 24),

        // Upload Profile Picture Section
        _buildUploadProfilePictureSection(),
      ],
    );
  }

  Widget _buildSubmitButton(AuthProvider authProvider) {
    return AuthButton(
      text: 'Complete Profile',
      onPressed: authProvider.isLoading ? null : _completeProfile,
      isLoading: authProvider.isLoading,
      icon: Icons.check_circle,
    );
  }

  Widget _buildUploadProfilePictureSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Picture (Optional)',
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.border),
          ),
          child: Column(
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  size: 32,
                  color: AppColors.primary,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Upload Profile Picture (Optional)',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Not required - you can add this later in settings',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSkipButton() {
    return Center(
      child: TextButton(
        onPressed: () {
          // Navigate to host dashboard without completing profile
          context.go('/host-dashboard');
        },
        child: Text(
          'Skip for now',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            decoration: TextDecoration.underline,
          ),
        ),
      ),
    );
  }

  void _completeProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = context.read<AuthProvider>();

    // Normalize date format (convert slashes to dashes)
    String normalizedDate = _dateOfBirthController.text.trim().replaceAll('/', '-');

    final request = CompleteProfileRequestModel(
      dateOfBirth: normalizedDate,
      address: _addressController.text.trim(),
      nidaNumber: _nidaController.text.trim(),
    );

    final success = await authProvider.completeProfile(request);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.successMessage ?? 'Profile completed successfully!'),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
        
        // Navigate to host dashboard
        context.go('/host-dashboard');
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(authProvider.errorMessage ?? 'Failed to complete profile'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }
}
