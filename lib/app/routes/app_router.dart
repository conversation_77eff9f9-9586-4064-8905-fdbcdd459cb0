import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/authentication/presentation/screens/splash_screen.dart';
import '../../features/authentication/presentation/screens/login_screen.dart';
import '../../features/authentication/presentation/screens/register_screen.dart';
import '../../features/authentication/presentation/screens/otp_verification_screen.dart';
import '../../features/authentication/presentation/screens/forgot_password_screen.dart';
import '../../features/authentication/presentation/screens/reset_password_screen.dart';
import '../../features/authentication/presentation/screens/profile_screen.dart';
import '../../features/home/<USER>/screens/customer_home_screen.dart';
import '../../features/home/<USER>/screens/landlord_home_screen.dart';
import '../../features/home/<USER>/screens/main_navigation_screen.dart';
import '../../features/home/<USER>/screens/account_settings_screen.dart';
import '../../features/home/<USER>/screens/about_screen.dart';
import '../../features/profile/presentation/screens/edit_profile_screen.dart';
import '../../features/profile/presentation/screens/change_password_screen.dart';
import '../../features/profile/presentation/screens/my_bookings_screen.dart';
import '../../features/profile/presentation/screens/settings_screen.dart';
import '../../features/profile/presentation/screens/wishlist_screen.dart';
import '../../features/profile/presentation/screens/payment_methods_screen.dart';
import '../../features/profile/presentation/screens/notifications_screen.dart';
import '../../features/support/presentation/screens/help_center_screen.dart';
import '../../features/support/presentation/screens/contact_support_screen.dart';
import '../../features/support/presentation/screens/send_feedback_screen.dart';
import '../../features/legal/presentation/screens/privacy_policy_screen.dart';
import '../../features/legal/presentation/screens/terms_of_service_screen.dart';
import '../../features/authentication/presentation/screens/complete_profile_screen.dart';
import '../../features/home_host/presentation/screens/host_dashboard_screen.dart';
import '../../features/home_host/presentation/screens/settings_screen.dart' as host_settings;
import '../../features/listing/routes/listing_routes.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    routes: [
      // Splash Screen
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/otp-verification',
        name: 'otp-verification',
        builder: (context, state) => const OtpVerificationScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/reset-password',
        name: 'reset-password',
        builder: (context, state) => const ResetPasswordScreen(),
      ),
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      
      // Main Navigation (for unauthenticated users)
      GoRoute(
        path: '/main-navigation',
        name: 'main-navigation',
        builder: (context, state) => const MainNavigationScreen(),
      ),

      // Home Routes
      GoRoute(
        path: '/customer-home',
        name: 'customer-home',
        builder: (context, state) => const CustomerHomeScreen(),
      ),
      GoRoute(
        path: '/landlord-home',
        name: 'landlord-home',
        builder: (context, state) => const LandlordHomeScreen(),
      ),

      // Complete Profile Route (Authentication)
      GoRoute(
        path: '/host-complete-profile',
        name: 'host-complete-profile',
        builder: (context, state) => const CompleteProfileScreen(),
      ),
      GoRoute(
        path: '/host-dashboard',
        name: 'host-dashboard',
        builder: (context, state) => const HostDashboardScreen(),
      ),

      // Host Listing Routes
      ...ListingRoutes.routes,

      // Account Settings Routes
      GoRoute(
        path: '/account-settings',
        name: 'account-settings',
        builder: (context, state) => const AccountSettingsScreen(),
      ),
      GoRoute(
        path: '/account-settings/about',
        name: 'about',
        builder: (context, state) => const AboutScreen(),
      ),
      GoRoute(
        path: '/account-settings/advanced',
        name: 'advanced-settings',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Advanced Settings - Coming Soon')),
        ),
      ),
      GoRoute(
        path: '/account-settings/feedback',
        name: 'feedback',
        builder: (context, state) => const Scaffold(
          body: Center(child: Text('Send Feedback - Coming Soon')),
        ),
      ),

      // Profile Routes
      GoRoute(
        path: '/profile/edit',
        name: 'edit-profile',
        builder: (context, state) => const EditProfileScreen(),
      ),
      GoRoute(
        path: '/profile/change-password',
        name: 'change-password',
        builder: (context, state) => const ChangePasswordScreen(),
      ),
      GoRoute(
        path: '/profile/settings',
        name: 'host-settings',
        builder: (context, state) => const host_settings.SettingsScreen(),
      ),
      GoRoute(
        path: '/profile/bookings',
        name: 'my-bookings',
        builder: (context, state) => const MyBookingsScreen(),
      ),
      GoRoute(
        path: '/profile/wishlist',
        name: 'wishlist',
        builder: (context, state) => const WishlistScreen(),
      ),
      GoRoute(
        path: '/profile/payment-methods',
        name: 'payment-methods',
        builder: (context, state) => const PaymentMethodsScreen(),
      ),
      GoRoute(
        path: '/profile/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),
      GoRoute(
        path: '/profile/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),

      // Support Routes
      GoRoute(
        path: '/support/help-center',
        name: 'help-center',
        builder: (context, state) => const HelpCenterScreen(),
      ),
      GoRoute(
        path: '/support/contact',
        name: 'contact-support',
        builder: (context, state) => const ContactSupportScreen(),
      ),
      GoRoute(
        path: '/support/feedback',
        name: 'send-feedback',
        builder: (context, state) => const SendFeedbackScreen(),
      ),

      // Legal Routes
      GoRoute(
        path: '/legal/privacy',
        name: 'privacy-policy',
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),
      GoRoute(
        path: '/legal/terms',
        name: 'terms-of-service',
        builder: (context, state) => const TermsOfServiceScreen(),
      ),
    ],
    
    // Error handling
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found: ${state.uri.toString()}',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
