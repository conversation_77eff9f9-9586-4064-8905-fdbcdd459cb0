import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../constants/app_constants.dart';
import 'storage_service.dart';
import '../utils/logger.dart';

class HttpClient {
  static HttpClient? _instance;
  late Dio _dio;
  
  //private constructor supporting internal use only 
  HttpClient._() {
    _initializeDio();
  }
  
  //getter to access private http  client that initialize dio  
  static HttpClient get instance {
    _instance ??= HttpClient._();
    return _instance!;
  }
  
  //global configuration with dio b4 initintialization
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConstants.baseUrlV1,
      connectTimeout: const Duration(milliseconds: AppConstants.connectionTimeout),
      receiveTimeout: const Duration(milliseconds: AppConstants.receiveTimeout),
      headers: {
        ApiConstants.headerContentType: ApiConstants.contentTypeJson,
        ApiConstants.headerAccept: ApiConstants.contentTypeJson,
        ApiConstants.headerUserAgent: 'EMakazi-Mobile/${AppConstants.appVersion}',
      },
    ));
    
    // Add interceptors
    _dio.interceptors.add(_AuthInterceptor());
    _dio.interceptors.add(_LoggingInterceptor());
    _dio.interceptors.add(_ErrorInterceptor());
  }
  

  /// request methods from the dio
  // GET request
  Future<Map<String, dynamic>> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      Logger.info('GET Request: $endpoint');
      Logger.info('Query Parameters: $queryParameters');
      
      // Merge headers with defaults instead of overriding
      final mergedHeaders = <String, dynamic>{
        ApiConstants.headerContentType: ApiConstants.contentTypeJson,
        ApiConstants.headerAccept: ApiConstants.contentTypeJson,
        ApiConstants.headerUserAgent: 'EMakazi-Mobile/${AppConstants.appVersion}',
        ...?headers, // Add any additional headers
      };

      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
        options: Options(headers: mergedHeaders),
      );
      
      Logger.info('GET Response: ${response.statusCode}');
      return _handleResponse(response);
    } catch (e) {
      Logger.error('GET Error: $e');
      rethrow;
    }
  }
  
  // POST request
  Future<Map<String, dynamic>> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      print('=== HTTP CLIENT POST START ===');
      print('POST Request: $endpoint');
      print('Base URL: ${ApiConstants.baseUrlV1}');
      print('Full URL: ${ApiConstants.baseUrlV1}$endpoint');
      print('Data: $data');
      print('Query Parameters: $queryParameters');
      print('Headers: $headers');
      Logger.info('=== HTTP CLIENT POST START ===');
      Logger.info('POST Request: $endpoint');
      Logger.info('Base URL: ${ApiConstants.baseUrlV1}');
      Logger.info('Full URL: ${ApiConstants.baseUrlV1}$endpoint');
      Logger.info('Data: $data');
      Logger.info('Query Parameters: $queryParameters');
      Logger.info('Headers: $headers');

      // Try with minimal headers first
      final mergedHeaders = <String, dynamic>{
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...?headers, // Add any additional headers
      };

      print('Merged Headers: $mergedHeaders');
      print('Raw Data Type: ${data.runtimeType}');
      print('Raw Data: $data');
      if (data is Map) {
        print('JSON Encoded Data: ${jsonEncode(data)}');
      }
      Logger.info('Merged Headers: $mergedHeaders');

      final response = await _dio.post(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(
          headers: mergedHeaders,
          contentType: Headers.jsonContentType,
          responseType: ResponseType.json,
          validateStatus: (status) {
            // Accept all status codes to see what we get
            return status != null && status < 600;
          },
        ),
      );

      print('POST Response Status: ${response.statusCode}');
      print('POST Response Headers: ${response.headers}');
      print('POST Response Data: ${response.data}');
      print('POST Response Data Type: ${response.data.runtimeType}');
      if (response.statusCode != null && response.statusCode! >= 400) {
        print('ERROR Response Body: ${response.data}');
      }
      Logger.info('POST Response Status: ${response.statusCode}');
      Logger.info('POST Response Headers: ${response.headers}');
      Logger.info('POST Response Data: ${response.data}');
      Logger.info('=== HTTP CLIENT POST SUCCESS ===');

      return _handleResponse(response);
    } catch (e, stackTrace) {
      print('POST Error: $e');
      print('Error type: ${e.runtimeType}');
      if (e is DioException) {
        print('DioException Response: ${e.response}');
        print('DioException Response Status: ${e.response?.statusCode}');
        print('DioException Response Data: ${e.response?.data}');
        print('DioException Message: ${e.message}');
        print('DioException Type: ${e.type}');
        Logger.error('DioException Response: ${e.response}');
        Logger.error('DioException Response Status: ${e.response?.statusCode}');
        Logger.error('DioException Response Data: ${e.response?.data}');
        Logger.error('DioException Message: ${e.message}');
        Logger.error('DioException Type: ${e.type}');
      }
      print('Stack trace: $stackTrace');
      Logger.error('POST Error: $e');
      Logger.error('Error type: ${e.runtimeType}');
      Logger.error('Stack trace: $stackTrace');
      Logger.error('=== HTTP CLIENT POST ERROR ===');
      rethrow;
    }
  }
  
  // PUT request
  Future<Map<String, dynamic>> put(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      Logger.info('PUT Request: $endpoint');
      Logger.info('Data: $data');
      
      // Merge headers with defaults instead of overriding
      final mergedHeaders = <String, dynamic>{
        ApiConstants.headerContentType: ApiConstants.contentTypeJson,
        ApiConstants.headerAccept: ApiConstants.contentTypeJson,
        ApiConstants.headerUserAgent: 'EMakazi-Mobile/${AppConstants.appVersion}',
        ...?headers, // Add any additional headers
      };

      final response = await _dio.put(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: mergedHeaders),
      );
      
      Logger.info('PUT Response: ${response.statusCode}');
      return _handleResponse(response);
    } catch (e) {
      Logger.error('PUT Error: $e');
      rethrow;
    }
  }

  // PATCH request
  Future<Map<String, dynamic>> patch(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      Logger.info('PATCH Request: $endpoint');
      Logger.info('Data: $data');

      // Merge headers with defaults instead of overriding
      final mergedHeaders = <String, dynamic>{
        ApiConstants.headerContentType: ApiConstants.contentTypeJson,
        ApiConstants.headerAccept: ApiConstants.contentTypeJson,
        ApiConstants.headerUserAgent: 'EMakazi-Mobile/${AppConstants.appVersion}',
        ...?headers, // Add any additional headers
      };

      final response = await _dio.patch(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: mergedHeaders),
      );

      Logger.info('PATCH Response: ${response.statusCode}');
      return _handleResponse(response);
    } catch (e) {
      Logger.error('PATCH Error: $e');
      rethrow;
    }
  }

  // DELETE request
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) async {
    try {
      Logger.info('DELETE Request: $endpoint');
      
      // Merge headers with defaults instead of overriding
      final mergedHeaders = <String, dynamic>{
        ApiConstants.headerContentType: ApiConstants.contentTypeJson,
        ApiConstants.headerAccept: ApiConstants.contentTypeJson,
        ApiConstants.headerUserAgent: 'EMakazi-Mobile/${AppConstants.appVersion}',
        ...?headers, // Add any additional headers
      };

      final response = await _dio.delete(
        endpoint,
        data: data,
        queryParameters: queryParameters,
        options: Options(headers: mergedHeaders),
      );
      
      Logger.info('DELETE Response: ${response.statusCode}');
      return _handleResponse(response);
    } catch (e) {
      Logger.error('DELETE Error: $e');
      rethrow;
    }
  }
  
  /// Upload file ---- study later 
  Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    File file, {
    String fieldName = 'file',
    Map<String, dynamic>? additionalData,
    Map<String, dynamic>? headers,
    void Function(int, int)? onSendProgress,
  }) async {
    try {
      Logger.info('Upload Request: $endpoint');
      Logger.info('File: ${file.path}');
      
      final formData = FormData.fromMap({
        fieldName: await MultipartFile.fromFile(file.path),
        if (additionalData != null) ...additionalData,
      });
      
      final response = await _dio.post(
        endpoint,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: onSendProgress,
      );
      
      Logger.info('Upload Response: ${response.statusCode}');
      return _handleResponse(response);
    } catch (e) {
      Logger.error('Upload Error: $e');
      rethrow;
    }
  }
  
  /// Download file ---stzudy later 
  Future<void> downloadFile(
    String url,
    String savePath, {
    void Function(int, int)? onReceiveProgress,
    Map<String, dynamic>? headers,
  }) async {
    try {
      Logger.info('Download Request: $url');
      Logger.info('Save Path: $savePath');
      
      await _dio.download(
        url,
        savePath,
        options: Options(headers: headers),
        onReceiveProgress: onReceiveProgress,
      );
      
      Logger.info('Download completed: $savePath');
    } catch (e) {
      Logger.error('Download Error: $e');
      rethrow;
    }
  }
  
  // Handle response and extract data
  Map<String, dynamic> _handleResponse(Response response) {
    try {
      print('=== HANDLE RESPONSE START ===');
      final statusCode = response.statusCode ?? 0;
      print('Status Code: $statusCode');
      print('Response Data Type: ${response.data.runtimeType}');
      print('Response Data: ${response.data}');

      Logger.info('=== HANDLE RESPONSE START ===');
      Logger.info('Status Code: $statusCode');
      Logger.info('Response Data Type: ${response.data.runtimeType}');
      Logger.info('Response Data: ${response.data}');

      if (statusCode >= 200 && statusCode < 300) {
        // Success response
        print('Success response detected');
        Logger.info('Success response detected');
        //response data should be returned in Map way (json-format-like)
        if (response.data is Map<String, dynamic>) {
          print('Response data is Map<String, dynamic>');
          Logger.info('Response data is Map<String, dynamic>');
          final result = response.data as Map<String, dynamic>;
          print('Returning: $result');
          print('=== HANDLE RESPONSE SUCCESS ===');
          Logger.info('Returning: $result');
          Logger.info('=== HANDLE RESPONSE SUCCESS ===');
          return result;
        } else if (response.data is List) {
          print('Response data is List, wrapping in data key');
          Logger.info('Response data is List, wrapping in data key');
          final result = {'data': response.data};
          print('Returning: $result');
          print('=== HANDLE RESPONSE SUCCESS ===');
          Logger.info('Returning: $result');
          Logger.info('=== HANDLE RESPONSE SUCCESS ===');
          return result;
        } else {
          print('Response data is other type, wrapping in data key');
          Logger.info('Response data is other type, wrapping in data key');
          final result = {'data': response.data};
          print('Returning: $result');
          print('=== HANDLE RESPONSE SUCCESS ===');
          Logger.info('Returning: $result');
          Logger.info('=== HANDLE RESPONSE SUCCESS ===');
          return result;
        }
      } else if (statusCode == 400 && response.data is Map<String, dynamic>) {
        // Handle 400 validation errors - return the response data so the app can handle it
        print('400 validation error detected, returning response data');
        Logger.info('400 validation error detected, returning response data');
        final result = response.data as Map<String, dynamic>;
        print('Returning validation error response: $result');
        print('=== HANDLE RESPONSE VALIDATION ERROR ===');
        Logger.info('Returning validation error response: $result');
        Logger.info('=== HANDLE RESPONSE VALIDATION ERROR ===');
        return result;
      } else {
        // Other error responses
        print('Error response detected');
        print('Status Code: $statusCode');
        print('Response Data: ${response.data}');
        print('=== HANDLE RESPONSE ERROR ===');
        Logger.error('Error response detected');
        Logger.error('Status Code: $statusCode');
        Logger.error('Response Data: ${response.data}');
        Logger.error('=== HANDLE RESPONSE ERROR ===');
        throw HttpException(
          statusCode: statusCode,
          message: 'HTTP Error: $statusCode',
          data: response.data,
        );
      }
    } catch (e, stackTrace) {
      print('_handleResponse ERROR: $e');
      print('Stack trace: $stackTrace');
      Logger.error('_handleResponse ERROR: $e');
      Logger.error('Stack trace: $stackTrace');
      rethrow;
    }
  }
}

// Auth Interceptor
///-study later the inteceptors .. 
class _AuthInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = StorageService.instance.getAuthToken();
    if (token != null) {
      options.headers[ApiConstants.headerAuthorization] = 'Bearer $token';
    }
    handler.next(options);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (err.response?.statusCode == ApiConstants.statusUnauthorized) {
      // Token expired, try to refresh
      final success = await _refreshToken();
      if (success) {
        // Retry the request
        final options = err.requestOptions;
        try {
          final response = await HttpClient.instance._dio.fetch(options);
          handler.resolve(response);
        } catch (e) {
          handler.next(err);
        }
      } else {
        // Redirect to login
        _logout();
        handler.next(err);
      }
    } else {
      handler.next(err);
    }
  }
  
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = StorageService.instance.getRefreshToken();
      if (refreshToken == null) return false;
      
      final response = await HttpClient.instance.post(
        ApiConstants.authRefresh,
        data: {'refresh_token': refreshToken},
      );
      
      final newToken = response['access_token'];
      final newRefreshToken = response['refresh_token'];
      
      await StorageService.instance.setAuthToken(newToken);
      await StorageService.instance.setRefreshToken(newRefreshToken);
      
      return true;
    } catch (e) {
      Logger.error('Token refresh failed: $e');
      return false;
    }
  }
  
  void _logout() {
    StorageService.instance.clearAuthData();
    // TODO: Navigate to login screen
  }
}

// Logging Interceptor
class _LoggingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      Logger.info('REQUEST[${options.method}] => PATH: ${options.path}');
      Logger.info('Headers: ${options.headers}');
      Logger.info('Data: ${options.data}');
    }
    handler.next(options);
  }
  
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      Logger.info('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
      Logger.info('Data: ${response.data}');
    }
    handler.next(response);
  }
  
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (kDebugMode) {
      Logger.error('ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}');
      Logger.error('Error: ${err.message}');
    }
    handler.next(err);
  }
}

// Error Interceptor
class _ErrorInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    HttpException httpException;
    
    if (err.response != null) {
      // Server responded with error
      httpException = HttpException(
        statusCode: err.response!.statusCode ?? 0,
        message: _getErrorMessage(err.response!),
        data: err.response!.data,
      );
    } else {
      // Network error or other issues
      httpException = HttpException(
        statusCode: 0,
        message: err.message ?? 'Unknown error occurred',
        data: null,
      );
    }
    
    Logger.error('HTTP Exception: ${httpException.toString()}');
    
    // Convert DioException to our custom HttpException
    handler.reject(DioException(
      requestOptions: err.requestOptions,
      response: err.response,
      type: err.type,
      error: httpException,
      message: httpException.message,
    ));
  }
  
  String _getErrorMessage(Response response) {
    try {
      if (response.data is Map<String, dynamic>) {
        final data = response.data as Map<String, dynamic>;
        return data['message'] ?? data['error'] ?? 'Server error';
      }
      return 'Server error';
    } catch (e) {
      return 'Server error';
    }
  }
}

// Custom HTTP Exception
class HttpException implements Exception {
  final int statusCode;
  final String message;
  final dynamic data;
  
  const HttpException({
    required this.statusCode,
    required this.message,
    this.data,
  });
  
  @override
  String toString() {
    return 'HttpException: $statusCode - $message';
  }
}